<!DOCTYPE html>
<html>
  <head>
    <title><%= content_for(:title) || "RapidMarkt - AI Marketing Platform" %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="turbo-cache-control" content="no-cache">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= yield :head %>

    <%# Enable PWA manifest for installable apps (make sure to enable in config/routes.rb too!) %>
    <%#= tag.link rel: "manifest", href: pwa_manifest_path(format: :json) %>

    <link rel="icon" href="/icon.png" type="image/png">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/icon.png">

    <%# Includes all stylesheet files in app/assets/stylesheets %>
    <%= stylesheet_link_tag :app, "data-turbo-track": "reload" %>
    <%= javascript_importmap_tags %>
  </head>

  <body class="bg-gray-50">
    <%= render 'shared/navbar' %>

    <% flash.each do |type, message| %>
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-4">
        <div class="rounded-md p-4 <%= type == 'notice' ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200' %>">
          <div class="flex">
            <div class="flex-shrink-0">
              <% if type == 'notice' %>
                <svg class="h-5 w-5 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              <% else %>
                <svg class="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              <% end %>
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium <%= type == 'notice' ? 'text-green-800' : 'text-red-800' %>">
                <%= message %>
              </p>
            </div>
            <div class="ml-auto pl-3">
              <div class="-mx-1.5 -my-1.5">
                <button type="button" class="inline-flex rounded-md p-1.5 <%= type == 'notice' ? 'text-green-500 hover:bg-green-100' : 'text-red-500 hover:bg-red-100' %> focus:outline-none focus:ring-2 focus:ring-offset-2 <%= type == 'notice' ? 'focus:ring-green-600' : 'focus:ring-red-600' %>" onclick="this.parentElement.parentElement.parentElement.parentElement.remove()">
                  <span class="sr-only">Dismiss</span>
                  <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    <% end %>

    <main class="min-h-screen">
      <%= yield %>
    </main>

    <%= render 'shared/footer' %>
  </body>
</html>
