<%= form_with model: template, local: true, class: "space-y-8" do |form| %>
  <!-- Error messages -->
  <% if template.errors.any? %>
    <div class="bg-red-50 border-l-4 border-red-400 p-6 rounded-lg">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">
            There <%= template.errors.count == 1 ? 'was' : 'were' %> <%= pluralize(template.errors.count, 'error') %> with your submission:
          </h3>
          <div class="mt-2 text-sm text-red-700">
            <ul role="list" class="list-disc list-inside space-y-1">
              <% template.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <!-- Template Category Selection -->
  <div class="bg-white shadow-lg rounded-xl border border-gray-100 p-8">
    <div class="mb-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-2">Template Category</h3>
      <p class="text-sm text-gray-600">Choose the type of email template you're creating</p>
    </div>
    
    <div class="grid grid-cols-1 gap-4 sm:grid-cols-3">
      <% [
        { value: 'newsletter', label: 'Newsletter', description: 'Regular updates and news', icon: 'M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z', color: 'blue' },
        { value: 'promotional', label: 'Promotional', description: 'Sales and special offers', icon: 'M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z', color: 'green' },
        { value: 'transactional', label: 'Transactional', description: 'Receipts and confirmations', icon: 'M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z', color: 'purple' }
      ].each do |category| %>
        <label class="relative cursor-pointer">
          <%= form.radio_button :template_type, category[:value], 
              class: "sr-only peer",
              checked: (template.template_type == category[:value] || (params[:category] == category[:value] && template.template_type.blank?)) %>
          <div class="flex items-center p-4 border-2 border-gray-200 rounded-xl hover:border-<%= category[:color] %>-300 peer-checked:border-<%= category[:color] %>-500 peer-checked:bg-<%= category[:color] %>-50 transition-all duration-200">
            <div class="w-10 h-10 bg-<%= category[:color] %>-100 rounded-lg flex items-center justify-center mr-4 peer-checked:bg-<%= category[:color] %>-200">
              <svg class="w-5 h-5 text-<%= category[:color] %>-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="<%= category[:icon] %>" clip-rule="evenodd" />
              </svg>
            </div>
            <div>
              <div class="font-medium text-gray-900"><%= category[:label] %></div>
              <div class="text-sm text-gray-500"><%= category[:description] %></div>
            </div>
          </div>
        </label>
      <% end %>
    </div>
  </div>

  <!-- Basic Template Details -->
  <div class="bg-white shadow-lg rounded-xl border border-gray-100 p-8">
    <div class="mb-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-2">Template Details</h3>
      <p class="text-sm text-gray-600">Basic information about your email template</p>
    </div>
    
    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
      <div>
        <%= form.label :name, class: "block text-sm font-medium text-gray-700 mb-2" do %>
          Template Name
          <span class="text-red-500">*</span>
        <% end %>
        <%= form.text_field :name, 
            class: "py-3 px-4 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition duration-200",
            placeholder: "e.g., Welcome Email, Monthly Newsletter" %>
        <p class="mt-2 text-sm text-gray-500">A descriptive name to help you identify this template</p>
      </div>
      
      <div>
        <%= form.label :subject, class: "block text-sm font-medium text-gray-700 mb-2" do %>
          Email Subject Line
          <span class="text-red-500">*</span>
        <% end %>
        <%= form.text_field :subject, 
            class: "py-3 px-4 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition duration-200",
            placeholder: "e.g., Welcome to {{company_name}}!" %>
        <p class="mt-2 text-sm text-gray-500">The subject line your recipients will see</p>
      </div>
    </div>
  </div>

  <!-- Email Content -->
  <div class="bg-white shadow-lg rounded-xl border border-gray-100 p-8">
    <div class="mb-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-2">Email Content</h3>
      <p class="text-sm text-gray-600">Design your email content with our rich text editor</p>
    </div>

    <!-- Variable Insertion Panel -->
    <div class="mb-6 p-4 bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl border border-indigo-100">
      <div class="flex items-center mb-3">
        <svg class="w-5 h-5 text-indigo-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-9a2 2 0 00-2-2h-2m-4 9l2-2 2 2m-2-2v6" />
        </svg>
        <h4 class="text-sm font-semibold text-indigo-900">Available Variables</h4>
      </div>
      <p class="text-sm text-indigo-700 mb-4">Click any variable below to insert it into your email content:</p>
      
      <div class="grid grid-cols-2 gap-3 sm:grid-cols-4">
        <% [
          { var: '{{first_name}}', label: 'First Name', desc: 'Contact\'s first name' },
          { var: '{{last_name}}', label: 'Last Name', desc: 'Contact\'s last name' },
          { var: '{{email}}', label: 'Email', desc: 'Contact\'s email address' },
          { var: '{{company_name}}', label: 'Company', desc: 'Your company name' },
          { var: '{{unsubscribe_url}}', label: 'Unsubscribe', desc: 'Unsubscribe link' },
          { var: '{{current_date}}', label: 'Current Date', desc: 'Today\'s date' },
          { var: '{{campaign_name}}', label: 'Campaign', desc: 'Campaign name' },
          { var: '{{custom_field}}', label: 'Custom Field', desc: 'Custom contact data' }
        ].each do |variable| %>
          <button type="button" 
                  onclick="insertVariable('<%= variable[:var] %>')"
                  class="group flex flex-col items-start p-3 text-left border border-indigo-200 rounded-lg hover:border-indigo-300 hover:bg-white transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-indigo-500">
            <div class="text-xs font-mono text-indigo-600 group-hover:text-indigo-700 mb-1"><%= variable[:var] %></div>
            <div class="text-xs font-medium text-gray-700"><%= variable[:label] %></div>
            <div class="text-xs text-gray-500"><%= variable[:desc] %></div>
          </button>
        <% end %>
      </div>
    </div>

    <!-- Rich Text Editor -->
    <div class="mb-4">
      <%= form.label :body, class: "block text-sm font-medium text-gray-700 mb-2" do %>
        Email Body
        <span class="text-red-500">*</span>
      <% end %>
      
      <!-- Toolbar -->
      <div class="border border-gray-300 rounded-t-lg bg-gray-50 p-2 flex flex-wrap items-center gap-1" id="editor-toolbar">
        <div class="flex items-center space-x-1 border-r border-gray-300 pr-2 mr-2">
          <button type="button" onclick="formatText('bold')" class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors" title="Bold">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M5 4a1 1 0 011-1h3a3 3 0 110 6H6v2h3a3 3 0 110 6H6a1 1 0 01-1-1V4zm2 2v2h2a1 1 0 100-2H7zm0 6v2h2a1 1 0 100-2H7z" clip-rule="evenodd" />
            </svg>
          </button>
          <button type="button" onclick="formatText('italic')" class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors" title="Italic">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M8 2a1 1 0 011 1v1h2a1 1 0 110 2h-.5l-1 8H11a1 1 0 110 2H9a1 1 0 01-1-1v-1H6a1 1 0 110-2h.5l1-8H7a1 1 0 110-2h1V3a1 1 0 011-1z" clip-rule="evenodd" />
            </svg>
          </button>
          <button type="button" onclick="formatText('underline')" class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors" title="Underline">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 19h14M8 5v8a4 4 0 008 0V5" />
            </svg>
          </button>
        </div>
        
        <div class="flex items-center space-x-1 border-r border-gray-300 pr-2 mr-2">
          <button type="button" onclick="formatText('justifyLeft')" class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors" title="Align Left">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h6a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h6a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd" />
            </svg>
          </button>
          <button type="button" onclick="formatText('justifyCenter')" class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors" title="Center">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm2 4a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zm-2 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm2 4a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1z" clip-rule="evenodd" />
            </svg>
          </button>
          <button type="button" onclick="formatText('justifyRight')" class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors" title="Align Right">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm6 4a1 1 0 011-1h6a1 1 0 110 2h-6a1 1 0 01-1-1zm-6 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm6 4a1 1 0 011-1h6a1 1 0 110 2h-6a1 1 0 01-1-1z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
        
        <div class="flex items-center space-x-1 border-r border-gray-300 pr-2 mr-2">
          <button type="button" onclick="formatText('insertUnorderedList')" class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors" title="Bullet List">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3 4a1 1 0 100 2h1a1 1 0 100-2H3zM8 5a1 1 0 011-1h6a1 1 0 110 2H9a1 1 0 01-1-1zM3 9a1 1 0 100 2h1a1 1 0 100-2H3zM9 10a1 1 0 011-1h5a1 1 0 110 2h-5a1 1 0 01-1-1zM3 14a1 1 0 100 2h1a1 1 0 100-2H3zM9 15a1 1 0 011-1h5a1 1 0 110 2h-5a1 1 0 01-1-1z" clip-rule="evenodd" />
            </svg>
          </button>
          <button type="button" onclick="formatText('insertOrderedList')" class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors" title="Numbered List">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
        
        <div class="flex items-center space-x-1">
          <button type="button" onclick="insertLink()" class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors" title="Insert Link">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
            </svg>
          </button>
          <button type="button" onclick="insertImage()" class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors" title="Insert Image">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </button>
        </div>
      </div>
      
      <!-- Editor Content -->
      <div id="editor" 
           contenteditable="true"
           class="min-h-96 p-4 border-l border-r border-b border-gray-300 rounded-b-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 bg-white"
           style="max-height: 500px; overflow-y: auto;"
           data-placeholder="Start typing your email content here...">
        <%= template.body.present? ? template.body.html_safe : '' %>
      </div>
      
      <!-- Hidden textarea to store the content -->
      <%= form.text_area :body, 
          id: "template_body_hidden",
          class: "hidden",
          value: template.body %>
    </div>
    
    <!-- Preview Section -->
    <% if template.persisted? %>
      <div class="mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
        <div class="flex items-center justify-between mb-3">
          <h4 class="text-sm font-medium text-gray-900">Template Preview</h4>
          <%= link_to preview_template_path(template), 
              target: "_blank",
              class: "inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
            <svg class="-ml-0.5 mr-1 h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
            </svg>
            Open Preview
          <% end %>
        </div>
        <p class="text-sm text-gray-600">Preview how your template will look to recipients with sample data.</p>
      </div>
    <% end %>
  </div>

  <!-- Form Actions -->
  <div class="bg-white shadow-lg rounded-xl border border-gray-100 p-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
      <div class="flex space-x-4">
        <%= link_to templates_path, 
            class: "inline-flex items-center px-6 py-3 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-200" do %>
          <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          Cancel
        <% end %>
        
        <% if template.persisted? %>
          <button type="button" 
                  onclick="saveAsDraft()"
                  class="inline-flex items-center px-6 py-3 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-200">
            <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12" />
            </svg>
            Save as Draft
          </button>
        <% end %>
      </div>
      
      <div class="flex space-x-4">
        <% if template.persisted? %>
          <%= link_to preview_template_path(template), 
              target: "_blank",
              class: "inline-flex items-center px-6 py-3 border border-indigo-300 shadow-sm text-sm font-medium rounded-lg text-indigo-700 bg-indigo-50 hover:bg-indigo-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-200" do %>
            <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
            Preview
          <% end %>
        <% end %>
        
        <%= form.submit template.persisted? ? "Update Template" : "Create Template", 
            class: "inline-flex items-center px-8 py-3 border border-transparent shadow-sm text-sm font-medium rounded-lg text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-200",
            onclick: "syncEditorContent()" %>
      </div>
    </div>
  </div>
<% end %>

<script>
  // Rich text editor functionality
  const editor = document.getElementById('editor');
  const hiddenTextarea = document.getElementById('template_body_hidden');
  
  // Initialize editor content
  if (hiddenTextarea.value && !editor.innerHTML.trim()) {
    editor.innerHTML = hiddenTextarea.value;
  }
  
  // Add placeholder functionality
  function checkPlaceholder() {
    if (editor.innerHTML.trim() === '' || editor.innerHTML === '<br>') {
      editor.classList.add('empty');
    } else {
      editor.classList.remove('empty');
    }
  }
  
  // Add CSS for placeholder
  const style = document.createElement('style');
  style.textContent = `
    #editor.empty:before {
      content: attr(data-placeholder);
      color: #9CA3AF;
      font-style: italic;
      pointer-events: none;
    }
    #editor:focus {
      outline: none;
    }
  `;
  document.head.appendChild(style);
  
  // Check placeholder on load
  checkPlaceholder();
  
  // Update hidden textarea when editor content changes
  editor.addEventListener('input', function() {
    hiddenTextarea.value = editor.innerHTML;
    checkPlaceholder();
  });
  
  editor.addEventListener('blur', function() {
    hiddenTextarea.value = editor.innerHTML;
  });
  
  // Formatting functions
  function formatText(command, value = null) {
    document.execCommand(command, false, value);
    editor.focus();
    hiddenTextarea.value = editor.innerHTML;
  }
  
  function insertLink() {
    const url = prompt('Enter the URL:');
    if (url) {
      formatText('createLink', url);
    }
  }
  
  function insertImage() {
    const url = prompt('Enter the image URL:');
    if (url) {
      formatText('insertImage', url);
    }
  }
  
  // Variable insertion function
  function insertVariable(variable) {
    const selection = window.getSelection();
    if (selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      if (editor.contains(range.commonAncestorContainer) || editor === range.commonAncestorContainer) {
        range.deleteContents();
        const span = document.createElement('span');
        span.className = 'variable-tag';
        span.style.cssText = 'background-color: #EEF2FF; color: #4338CA; padding: 2px 6px; border-radius: 4px; font-family: monospace; font-size: 0.875em; border: 1px solid #C7D2FE;';
        span.textContent = variable;
        range.insertNode(span);
        
        // Move cursor after the inserted variable
        range.setStartAfter(span);
        range.setEndAfter(span);
        selection.removeAllRanges();
        selection.addRange(range);
      } else {
        // If no selection in editor, append to end
        const span = document.createElement('span');
        span.className = 'variable-tag';
        span.style.cssText = 'background-color: #EEF2FF; color: #4338CA; padding: 2px 6px; border-radius: 4px; font-family: monospace; font-size: 0.875em; border: 1px solid #C7D2FE;';
        span.textContent = variable;
        editor.appendChild(span);
        editor.appendChild(document.createTextNode(' '));
      }
    } else {
      // No selection, append to end
      const span = document.createElement('span');
      span.className = 'variable-tag';
      span.style.cssText = 'background-color: #EEF2FF; color: #4338CA; padding: 2px 6px; border-radius: 4px; font-family: monospace; font-size: 0.875em; border: 1px solid #C7D2FE;';
      span.textContent = variable;
      editor.appendChild(span);
      editor.appendChild(document.createTextNode(' '));
    }
    
    hiddenTextarea.value = editor.innerHTML;
    editor.focus();
    checkPlaceholder();
  }
  
  // Sync editor content before form submission
  function syncEditorContent() {
    hiddenTextarea.value = editor.innerHTML;
  }
  
  // Save as draft function
  function saveAsDraft() {
    syncEditorContent();
    // Add draft status to form data
    const form = document.querySelector('form');
    const draftInput = document.createElement('input');
    draftInput.type = 'hidden';
    draftInput.name = 'template[status]';
    draftInput.value = 'draft';
    form.appendChild(draftInput);
    form.submit();
  }
  
  // Prevent form submission on Enter in editor
  editor.addEventListener('keydown', function(e) {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      syncEditorContent();
      document.querySelector('form').submit();
    }
  });
</script>