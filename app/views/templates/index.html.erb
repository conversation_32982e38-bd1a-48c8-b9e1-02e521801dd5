<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Enhanced Page Header -->
    <div class="relative overflow-hidden bg-white/80 backdrop-blur-sm shadow-2xl rounded-3xl border border-white/20 p-8 mb-8">
      <div class="absolute inset-0 bg-gradient-to-r from-indigo-500/5 to-purple-500/5"></div>
      <div class="relative flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div class="flex-1 min-w-0">
          <!-- Enhanced Breadcrumb -->
          <nav class="flex mb-6" aria-label="Breadcrumb">
            <ol role="list" class="flex items-center space-x-4 bg-white/70 backdrop-blur-sm rounded-full px-6 py-3 shadow-sm border border-white/20">
              <li>
                <div class="flex">
                  <%= link_to dashboard_path, class: "text-gray-500 hover:text-indigo-600 transition-colors duration-200" do %>
                    <svg class="flex-shrink-0 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                    </svg>
                    <span class="sr-only">Home</span>
                  <% end %>
                </div>
              </li>
              <li>
                <div class="flex items-center">
                  <svg class="flex-shrink-0 h-4 w-4 text-gray-300" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                  </svg>
                  <span class="ml-3 text-sm font-medium text-indigo-600">Templates</span>
                </div>
              </li>
            </ol>
          </nav>
          <!-- Enhanced Page Title -->
          <div class="text-center lg:text-left">
            <div class="flex items-center justify-center lg:justify-start mb-4">
              <div class="w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <div>
                <h1 class="text-4xl font-bold bg-gradient-to-r from-gray-900 via-indigo-800 to-purple-800 bg-clip-text text-transparent mb-2">
                  Email Templates
                </h1>
                <p class="text-lg text-gray-600">
                  Create stunning, reusable email templates for your campaigns
                </p>
              </div>
            </div>
          </div>
        </div>
        <div class="mt-8 lg:mt-0 lg:ml-6 flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4">
          <!-- Enhanced Template Categories Dropdown -->
          <div class="relative inline-block text-left" data-controller="dropdown">
            <button type="button"
                    class="inline-flex items-center px-6 py-3 border border-white/30 shadow-lg text-sm font-medium rounded-xl text-gray-700 bg-white/80 backdrop-blur-sm hover:bg-white hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200"
                    data-action="click->dropdown#toggle"
                    data-dropdown-target="button">
              <svg class="-ml-1 mr-2 h-5 w-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14-4l-3 3.5M5 7l3 3.5m6 1L9 15.5M15 11l3 3.5" />
              </svg>
              Browse Categories
              <svg class="ml-2 -mr-1 h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
              </svg>
            </button>
            <div class="origin-top-right absolute right-0 mt-3 w-64 rounded-2xl shadow-2xl bg-white/95 backdrop-blur-lg ring-1 ring-black/10 focus:outline-none hidden z-20"
                 data-dropdown-target="menu">
              <div class="py-2" role="menu" aria-orientation="vertical">
                <%= link_to new_template_path(category: 'newsletter'),
                           class: "group flex items-center px-5 py-3 text-sm text-gray-700 hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 hover:text-indigo-700 transition-all duration-200 mx-2 rounded-xl",
                           role: "menuitem" do %>
                  <div class="mr-3 h-8 w-8 rounded-lg bg-blue-100 flex items-center justify-center group-hover:bg-blue-200 transition-colors">
                    <svg class="h-4 w-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                      <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                    </svg>
                  </div>
                  <div>
                    <div class="font-medium">Newsletter Template</div>
                    <div class="text-xs text-gray-500">Regular updates & news</div>
                  </div>
                <% end %>
                <%= link_to new_template_path(category: 'promotional'),
                           class: "group flex items-center px-5 py-3 text-sm text-gray-700 hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 hover:text-indigo-700 transition-all duration-200 mx-2 rounded-xl",
                           role: "menuitem" do %>
                  <div class="mr-3 h-8 w-8 rounded-lg bg-yellow-100 flex items-center justify-center group-hover:bg-yellow-200 transition-colors">
                    <svg class="h-4 w-4 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clip-rule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <div class="font-medium">Promotional Template</div>
                    <div class="text-xs text-gray-500">Sales & marketing campaigns</div>
                  </div>
                <% end %>
                <%= link_to new_template_path(category: 'transactional'),
                           class: "group flex items-center px-5 py-3 text-sm text-gray-700 hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 hover:text-indigo-700 transition-all duration-200 mx-2 rounded-xl",
                           role: "menuitem" do %>
                  <div class="mr-3 h-8 w-8 rounded-lg bg-green-100 flex items-center justify-center group-hover:bg-green-200 transition-colors">
                    <svg class="h-4 w-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <div class="font-medium">Transactional Template</div>
                    <div class="text-xs text-gray-500">Receipts & confirmations</div>
                  </div>
                <% end %>
                <div class="border-t border-gray-100 my-2 mx-2"></div>
                <%= link_to new_template_path,
                           class: "group flex items-center px-5 py-3 text-sm text-gray-700 hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 hover:text-indigo-700 transition-all duration-200 mx-2 rounded-xl",
                           role: "menuitem" do %>
                  <div class="mr-3 h-8 w-8 rounded-lg bg-purple-100 flex items-center justify-center group-hover:bg-purple-200 transition-colors">
                    <svg class="h-4 w-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                    </svg>
                  </div>
                  <div>
                    <div class="font-medium">Custom Template</div>
                    <div class="text-xs text-gray-500">Build from scratch</div>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
          
          <%= link_to new_template_path, 
              class: "inline-flex items-center px-6 py-3 border border-transparent shadow-sm text-sm font-medium rounded-lg text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-200" do %>
            <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
            New Template
          <% end %>
        </div>
      </div>
    </div>

    <!-- Enhanced Search and Filters -->
    <div class="bg-white/80 backdrop-blur-lg shadow-2xl rounded-2xl border border-white/30 p-8 mb-10 hover:shadow-3xl transition-all duration-500">
      <div class="mb-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-2 flex items-center">
          <svg class="w-5 h-5 text-indigo-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
          Find Your Perfect Template
        </h3>
        <p class="text-sm text-gray-600">Search and filter through our collection of professional email templates</p>
      </div>
      
      <%= form_with url: templates_path, method: :get, local: true, class: "space-y-6" do |form| %>
        <div class="grid grid-cols-1 lg:grid-cols-12 gap-4 items-end">
          <!-- Search Input -->
          <div class="lg:col-span-6">
            <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Search Templates</label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <%= form.text_field :search, 
                  value: params[:search],
                  placeholder: "Search by name, subject, or content...",
                  class: "pl-10 py-3 px-4 block w-full border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm transition-all duration-200 bg-gray-50/50 hover:bg-white focus:bg-white" %>
            </div>
          </div>
          
          <!-- Category Filter -->
          <div class="lg:col-span-3">
            <label for="category" class="block text-sm font-medium text-gray-700 mb-2">Category</label>
            <%= form.select :category, 
                options_for_select([['All Categories', ''], ['Newsletter', 'newsletter'], ['Promotional', 'promotional'], ['Transactional', 'transactional']], params[:category]),
                {},
                { class: "py-3 px-4 border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm transition-all duration-200 bg-gray-50/50 hover:bg-white focus:bg-white w-full" } %>
          </div>
          
          <!-- Action Buttons -->
          <div class="lg:col-span-3 flex space-x-3">
            <%= form.submit "Search", 
                class: "flex-1 inline-flex items-center justify-center px-6 py-3 border border-transparent rounded-xl shadow-lg text-sm font-medium text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 hover:shadow-xl hover:-translate-y-0.5" %>
            <% if params[:search].present? || params[:category].present? %>
              <%= link_to templates_path, 
                  class: "inline-flex items-center justify-center px-4 py-3 border border-gray-200 rounded-xl shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 hover:shadow-md" do %>
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              <% end %>
            <% end %>
          </div>
        </div>
        
        <!-- Active Filters Display -->
        <% if params[:search].present? || params[:category].present? %>
          <div class="flex items-center space-x-2 pt-4 border-t border-gray-100">
            <span class="text-sm text-gray-500">Active filters:</span>
            <% if params[:search].present? %>
              <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                Search: "<%= params[:search] %>"
              </span>
            <% end %>
            <% if params[:category].present? %>
              <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                Category: <%= params[:category].humanize %>
              </span>
            <% end %>
          </div>
        <% end %>
      <% end %>
    </div>

    <!-- Templates grid -->
    <div class="bg-white shadow-lg rounded-xl border border-gray-100 p-6">
      <% if @templates.any? %>
        <div class="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
          <% @templates.each do |template| %>
            <div class="group bg-white overflow-hidden shadow-lg rounded-2xl border-0 hover:shadow-2xl hover:-translate-y-2 transition-all duration-500 relative"
                 style="box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(0, 0, 0, 0.05);">
              <!-- Gradient overlay for visual appeal -->
              <div class="absolute inset-0 bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

              <!-- Template type indicator -->
              <div class="absolute top-4 left-4 z-10">
                <% category_colors = {
                  'newsletter' => 'from-blue-100 to-blue-200 text-blue-700 border-blue-200',
                  'promotional' => 'from-green-100 to-green-200 text-green-700 border-green-200',
                  'transactional' => 'from-purple-100 to-purple-200 text-purple-700 border-purple-200'
                } %>
                <div class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r <%= category_colors[template.template_type] || 'from-gray-100 to-gray-200 text-gray-700 border-gray-200' %> border">
                  <% if template.template_type == 'newsletter' %>
                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                      <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                    </svg>
                  <% elsif template.template_type == 'promotional' %>
                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clip-rule="evenodd" />
                    </svg>
                  <% elsif template.template_type == 'transactional' %>
                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                    </svg>
                  <% else %>
                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                      <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                    </svg>
                  <% end %>
                  <%= template.template_type&.humanize || 'Email Template' %>
                </div>
              </div>

              <div class="relative z-10 p-8">
              <div class="flex items-start justify-between mb-6">
                <div class="flex-1 min-w-0">
                  <h3 class="text-xl font-bold text-gray-900 mb-2 group-hover:text-indigo-700 transition-colors duration-300">
                    <%= link_to template.name, template_path(template), class: "hover:text-indigo-600 line-clamp-2" %>
                  </h3>
                  <p class="text-sm text-gray-600 line-clamp-2 leading-relaxed">
                    <%= template.subject %>
                  </p>
                </div>
                <div class="ml-4 flex-shrink-0">
                  <div class="relative inline-block text-left" data-controller="dropdown">
                    <button type="button"
                            class="w-10 h-10 bg-gray-100 group-hover:bg-white rounded-xl flex items-center justify-center text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 shadow-sm hover:shadow-md"
                            data-action="click->dropdown#toggle"
                            data-dropdown-target="button">
                      <span class="sr-only">Open options</span>
                      <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
                      </svg>
                    </button>
                    <div class="origin-top-right absolute right-0 mt-2 w-56 rounded-2xl shadow-xl bg-white border-0 focus:outline-none hidden z-20"
                         data-dropdown-target="menu"
                         style="box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05);">
                      <div class="py-1">
                        <%= link_to template_path(template), 
                            class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" do %>
                          <svg class="mr-3 h-5 w-5 text-gray-400 inline" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                            <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
                          </svg>
                          View
                        <% end %>
                        <%= link_to edit_template_path(template), 
                            class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" do %>
                          <svg class="mr-3 h-5 w-5 text-gray-400 inline" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                          </svg>
                          Edit
                        <% end %>
                        <%= link_to preview_template_path(template), 
                            class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",
                            target: "_blank" do %>
                          <svg class="mr-3 h-5 w-5 text-gray-400 inline" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 11-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12z" clip-rule="evenodd" />
                          </svg>
                          Preview
                        <% end %>
                        <%= link_to duplicate_template_path(template), 
                            data: { turbo_method: :post },
                            class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" do %>
                          <svg class="mr-3 h-5 w-5 text-gray-400 inline" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M7 9a2 2 0 012-2h6a2 2 0 012 2v6a2 2 0 01-2 2H9a2 2 0 01-2-2V9z" />
                            <path d="M5 3a2 2 0 00-2 2v6a2 2 0 002 2V5h8a2 2 0 00-2-2H5z" />
                          </svg>
                          Duplicate
                        <% end %>
                        <div class="border-t border-gray-100"></div>
                        <%= link_to template_path(template), 
                            data: { turbo_method: :delete, turbo_confirm: 'Are you sure you want to delete this template? This action cannot be undone.' },
                            class: "block px-4 py-2 text-sm text-red-700 hover:bg-red-50" do %>
                          <svg class="mr-3 h-5 w-5 text-red-400 inline" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" clip-rule="evenodd" />
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414L7.586 12l-1.293 1.293a1 1 0 101.414 1.414L9 13.414l2.293 2.293a1 1 0 001.414-1.414L11.414 12l1.293-1.293z" clip-rule="evenodd" />
                          </svg>
                          Delete
                        <% end %>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- Template Preview -->
              <div class="mb-6">
                <% if template.body.present? %>
                  <div class="bg-gray-50 group-hover:bg-white rounded-xl p-4 border border-gray-100 group-hover:border-indigo-100 transition-all duration-300">
                    <div class="text-sm text-gray-700 line-clamp-4 leading-relaxed">
                      <%= truncate(strip_tags(template.body), length: 150) %>
                    </div>
                  </div>
                <% else %>
                  <div class="bg-gray-50 group-hover:bg-white rounded-xl p-4 border border-gray-100 group-hover:border-indigo-100 transition-all duration-300">
                    <div class="text-sm text-gray-500 italic">
                      No content preview available
                    </div>
                  </div>
                <% end %>
              </div>

              <!-- Template Meta -->
              <div class="mb-6 flex items-center justify-between text-xs text-gray-500">
                <div class="flex items-center space-x-4">
                  <div class="flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                    </svg>
                    <%= time_ago_in_words(template.created_at) %> ago
                  </div>
                  <div class="flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                    </svg>
                    <% campaigns_count = template.campaigns.count %>
                    <%= pluralize(campaigns_count, 'use') %>
                  </div>
                </div>
                <div class="px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs font-medium">
                  Active
                </div>
              </div>
              
              <!-- Action Buttons -->
              <div class="flex space-x-3">
                <%= link_to template_path(template),
                    class: "flex-1 inline-flex items-center justify-center px-4 py-3 border-2 border-gray-200 text-sm font-semibold rounded-xl text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 group-hover:border-indigo-200 group-hover:text-indigo-700" do %>
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                  </svg>
                  View
                <% end %>
                <%= link_to edit_template_path(template),
                    class: "flex-1 inline-flex items-center justify-center px-4 py-3 border-2 border-transparent text-sm font-semibold rounded-xl text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" do %>
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                  </svg>
                  Edit
                <% end %>
              </div>
            </div>
          </div>
        <% end %>
      </div>
      
      <!-- Pagination -->
      <%= paginate @templates if defined?(Kaminari) %>
    <% else %>
        <!-- Empty state -->
        <div class="text-center py-16 px-6">
          <div class="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-full flex items-center justify-center">
            <svg class="w-8 h-8 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-gray-900 mb-2">
            <% if params[:search].present? || params[:category].present? %>
              No templates found
            <% else %>
              No email templates yet
            <% end %>
          </h3>
          <p class="text-gray-600 mb-8 max-w-md mx-auto">
            <% if params[:search].present? || params[:category].present? %>
              Try adjusting your search terms or create a new template to get started.
            <% else %>
              Get started by creating your first email template to streamline your campaigns.
            <% end %>
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <%= link_to new_template_path, 
                class: "inline-flex items-center px-6 py-3 border border-transparent shadow-sm text-sm font-medium rounded-lg text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-200" do %>
              <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
              Create Your First Template
            <% end %>
            <a href="#" class="inline-flex items-center px-6 py-3 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-200">
              <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
              </svg>
              Browse Template Gallery
            </a>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>