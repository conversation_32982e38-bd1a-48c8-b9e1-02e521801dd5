<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 min-h-full mt-2 mb-12">
  <!-- Breadcrumb -->
  <nav class="flex mb-8" aria-label="Breadcrumb">
    <ol role="list" class="flex items-center space-x-4">
      <li>
        <%= link_to root_path, class: "text-gray-400 hover:text-gray-500" do %>
          <svg class="flex-shrink-0 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path d="M10.707 2.293a1 1 0 00-1.414 0l-9 9a1 1 0 001.414 1.414L8 5.414V17a1 1 0 001 1h2a1 1 0 001-1v-3a1 1 0 011-1h2a1 1 0 011 1v3a1 1 0 001 1h2a1 1 0 001-1V5.414l6.293 6.293a1 1 0 001.414-1.414l-9-9z" />
          </svg>
          <span class="sr-only">Home</span>
        <% end %>
      </li>
      <li>
        <div class="flex items-center">
          <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
          </svg>
          <%= link_to "Templates", templates_path, class: "ml-4 text-sm font-medium text-gray-500 hover:text-gray-700" %>
        </div>
      </li>
      <li>
        <div class="flex items-center">
          <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
          </svg>
          <span class="ml-4 text-sm font-medium text-gray-500" aria-current="page">New Template</span>
        </div>
      </li>
    </ol>
  </nav>

  <!-- Page Header -->
  <div class="bg-white shadow-lg rounded-xl border border-gray-100 mb-8">
    <div class="px-8 py-6">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-12 h-12 bg-indigo-100 rounded-xl flex items-center justify-center">
            <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          </div>
        </div>
        <div class="ml-4">
          <h1 class="text-2xl font-bold text-gray-900">Create New Template</h1>
          <p class="mt-1 text-sm text-gray-500">
            Design a professional email template for your marketing campaigns. Use variables to personalize content for each recipient.
          </p>
        </div>
      </div>
    </div>
  </div>

  <!-- Template Creation Tips -->
  <div class="bg-blue-50 border border-blue-200 rounded-xl p-6 mb-8">
    <div class="flex">
      <div class="flex-shrink-0">
        <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
        </svg>
      </div>
      <div class="ml-3">
        <h3 class="text-sm font-medium text-blue-800">Template Creation Tips</h3>
        <div class="mt-2 text-sm text-blue-700">
          <ul class="list-disc list-inside space-y-1">
            <li>Use descriptive names and subject lines to make templates easy to find</li>
            <li>Include personalization variables like {{first_name}} to increase engagement</li>
            <li>Always include an unsubscribe link using {{unsubscribe_url}}</li>
            <li>Test your template with the preview function before using it in campaigns</li>
            <li>Choose the appropriate template type to help organize your templates</li>
          </ul>
        </div>
      </div>
    </div>
  </div>

  <!-- Template Form -->
  <div class="bg-white shadow-lg rounded-xl border border-gray-100">
    <div class="px-8 py-6">
      <%= render 'form', template: @template %>
    </div>
  </div>
</div>