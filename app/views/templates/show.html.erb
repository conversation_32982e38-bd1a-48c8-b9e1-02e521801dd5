<div class="min-h-full">
  <!-- Breadcrumb -->
  <nav class="flex mb-8" aria-label="Breadcrumb">
    <ol role="list" class="flex items-center space-x-4">
      <li>
        <%= link_to root_path, class: "text-gray-400 hover:text-gray-500" do %>
          <svg class="flex-shrink-0 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path d="M10.707 2.293a1 1 0 00-1.414 0l-9 9a1 1 0 001.414 1.414L8 5.414V17a1 1 0 001 1h2a1 1 0 001-1v-3a1 1 0 011-1h2a1 1 0 011 1v3a1 1 0 001 1h2a1 1 0 001-1V5.414l6.293 6.293a1 1 0 001.414-1.414l-9-9z" />
          </svg>
          <span class="sr-only">Home</span>
        <% end %>
      </li>
      <li>
        <div class="flex items-center">
          <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
          </svg>
          <%= link_to "Templates", templates_path, class: "ml-4 text-sm font-medium text-gray-500 hover:text-gray-700" %>
        </div>
      </li>
      <li>
        <div class="flex items-center">
          <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
          </svg>
          <span class="ml-4 text-sm font-medium text-gray-500" aria-current="page"><%= @template.name %></span>
        </div>
      </li>
    </ol>
  </nav>

  <!-- Page Header -->
  <div class="bg-white shadow-lg rounded-xl border border-gray-100 mb-8">
    <div class="px-8 py-6">
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div class="flex-1 min-w-0">
          <div class="flex items-center mb-2">
            <div class="flex-shrink-0">
              <% case @template.template_type %>
              <% when 'newsletter' %>
                <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                  <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                  </svg>
                </div>
              <% when 'promotional' %>
                <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                  <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clip-rule="evenodd" />
                  </svg>
                </div>
              <% when 'transactional' %>
                <div class="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                  <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                  </svg>
                </div>
              <% else %>
                <div class="w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center">
                  <svg class="w-6 h-6 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd" />
                  </svg>
                </div>
              <% end %>
            </div>
            <div class="ml-4">
              <h1 class="text-2xl font-bold text-gray-900"><%= @template.name %></h1>
              <div class="flex items-center mt-1 space-x-4">
                <span class="text-sm text-gray-500">Subject: <%= @template.subject %></span>
                <% if @template.template_type.present? %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 capitalize">
                    <%= @template.template_type %>
                  </span>
                <% end %>
              </div>
            </div>
          </div>
          
          <!-- Action Buttons -->
          <div class="mt-6 lg:mt-0 lg:ml-4">
            <div class="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3">
              <%= link_to edit_template_path(@template), 
                  class: "inline-flex items-center justify-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-200" do %>
                <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                Edit
              <% end %>
              
              <%= link_to preview_template_path(@template), 
                  target: "_blank",
                  class: "inline-flex items-center justify-center px-4 py-2 border border-indigo-300 shadow-sm text-sm font-medium rounded-lg text-indigo-700 bg-indigo-50 hover:bg-indigo-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-200" do %>
                <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                Preview
              <% end %>
              
              <%= link_to duplicate_template_path(@template), 
                  method: :post,
                  class: "inline-flex items-center justify-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-200" do %>
                <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
                Duplicate
              <% end %>
              
              <%= link_to template_path(@template), 
                  method: :delete,
                  data: { 
                    confirm: "Are you sure you want to delete this template? This action cannot be undone.",
                    turbo_method: :delete 
                  },
                  class: "inline-flex items-center justify-center px-4 py-2 border border-red-300 shadow-sm text-sm font-medium rounded-lg text-red-700 bg-red-50 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-200" do %>
                <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H8a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                Delete
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Template Details -->
  <div class="mt-8 grid grid-cols-1 gap-6 lg:grid-cols-3">
    <!-- Template Information -->
    <div class="lg:col-span-2">
      <div class="bg-white shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900">Template Information</h3>
          <p class="mt-1 max-w-2xl text-sm text-gray-500">Details about this email template.</p>
        </div>
        <div class="border-t border-gray-200">
          <dl>
            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt class="text-sm font-medium text-gray-500">Template name</dt>
              <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2"><%= @template.name %></dd>
            </div>
            <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt class="text-sm font-medium text-gray-500">Subject line</dt>
              <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2"><%= @template.subject %></dd>
            </div>
            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt class="text-sm font-medium text-gray-500">Created</dt>
              <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2"><%= @template.created_at.strftime("%B %d, %Y at %I:%M %p") %></dd>
            </div>
            <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt class="text-sm font-medium text-gray-500">Last updated</dt>
              <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2"><%= @template.updated_at.strftime("%B %d, %Y at %I:%M %p") %></dd>
            </div>
          </dl>
        </div>
      </div>
      
      <!-- Email Content Preview -->
      <div class="mt-6 bg-white shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900">Email Content</h3>
          <p class="mt-1 max-w-2xl text-sm text-gray-500">Preview of the email template content.</p>
        </div>
        <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
          <% if @template.body.present? %>
            <div class="prose max-w-none">
              <div class="border border-gray-200 rounded-lg p-4 bg-gray-50">
                <iframe srcdoc="<%= html_escape(@template.body) %>" 
                        class="w-full h-96 border-0 rounded"
                        sandbox="allow-same-origin">
                </iframe>
              </div>
            </div>
            
            <div class="mt-4">
              <details class="group">
                <summary class="flex cursor-pointer items-center gap-2 text-sm font-medium text-gray-700 hover:text-gray-900">
                  <svg class="h-4 w-4 transition-transform group-open:rotate-90" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                  </svg>
                  View HTML Source
                </summary>
                <div class="mt-2">
                  <pre class="bg-gray-100 p-4 rounded-lg text-xs overflow-x-auto"><code><%= @template.body %></code></pre>
                </div>
              </details>
            </div>
          <% else %>
            <p class="text-gray-500 italic">No content available for this template.</p>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Usage Statistics -->
    <div class="lg:col-span-1">
      <div class="bg-white shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900">Usage Statistics</h3>
          <p class="mt-1 max-w-2xl text-sm text-gray-500">How this template is being used.</p>
        </div>
        <div class="border-t border-gray-200">
          <dl>
            <div class="bg-gray-50 px-4 py-5 sm:px-6">
              <dt class="text-sm font-medium text-gray-500">Campaigns using this template</dt>
              <dd class="mt-1 text-3xl font-semibold text-gray-900"><%= @template.campaigns.count %></dd>
            </div>
            <div class="bg-white px-4 py-5 sm:px-6">
              <dt class="text-sm font-medium text-gray-500">Total emails sent</dt>
              <dd class="mt-1 text-3xl font-semibold text-gray-900"><%= @template.campaigns.joins(:campaign_contacts).where.not(campaign_contacts: { sent_at: nil }).count %></dd>
            </div>
            <div class="bg-gray-50 px-4 py-5 sm:px-6">
              <dt class="text-sm font-medium text-gray-500">Total emails opened</dt>
              <dd class="mt-1 text-3xl font-semibold text-gray-900"><%= @template.campaigns.joins(:campaign_contacts).where.not(campaign_contacts: { opened_at: nil }).count %></dd>
            </div>
            <div class="bg-white px-4 py-5 sm:px-6">
              <dt class="text-sm font-medium text-gray-500">Average open rate</dt>
              <dd class="mt-1 text-lg font-semibold text-gray-900">
                <% total_sent = @template.campaigns.joins(:campaign_contacts).where.not(campaign_contacts: { sent_at: nil }).count %>
                <% total_opened = @template.campaigns.joins(:campaign_contacts).where.not(campaign_contacts: { opened_at: nil }).count %>
                <% if total_sent > 0 %>
                  <%= number_to_percentage((total_opened.to_f / total_sent) * 100, precision: 1) %>
                <% else %>
                  <span class="text-gray-500">N/A</span>
                <% end %>
              </dd>
            </div>
          </dl>
        </div>
      </div>
      
      <!-- Recent Campaigns -->
      <div class="mt-6 bg-white shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900">Recent Campaigns</h3>
          <p class="mt-1 max-w-2xl text-sm text-gray-500">Latest campaigns using this template.</p>
        </div>
        <div class="border-t border-gray-200">
          <% recent_campaigns = @template.campaigns.order(created_at: :desc).limit(5) %>
          <% if recent_campaigns.any? %>
            <ul class="divide-y divide-gray-200">
              <% recent_campaigns.each do |campaign| %>
                <li class="px-4 py-4">
                  <div class="flex items-center justify-between">
                    <div class="flex-1 min-w-0">
                      <%= link_to campaign_path(campaign), class: "text-sm font-medium text-indigo-600 hover:text-indigo-500 truncate" do %>
                        <%= campaign.name %>
                      <% end %>
                      <p class="text-xs text-gray-500 mt-1">
                        <%= time_ago_in_words(campaign.created_at) %> ago
                      </p>
                    </div>
                    <div class="ml-4 flex-shrink-0">
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= campaign.status == 'sent' ? 'bg-green-100 text-green-800' : campaign.status == 'scheduled' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800' %>">
                        <%= campaign.status.humanize %>
                      </span>
                    </div>
                  </div>
                </li>
              <% end %>
            </ul>
            <div class="px-4 py-3 bg-gray-50">
              <%= link_to campaigns_path(template_id: @template.id), 
                  class: "text-sm text-indigo-600 hover:text-indigo-500" do %>
                View all campaigns using this template →
              <% end %>
            </div>
          <% else %>
            <div class="px-4 py-6 text-center">
              <p class="text-sm text-gray-500">No campaigns have used this template yet.</p>
              <%= link_to new_campaign_path(template_id: @template.id), 
                  class: "mt-2 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
                Create Campaign
              <% end %>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  function toggleView(view) {
    const previewView = document.getElementById('preview-view');
    const sourceView = document.getElementById('source-view');
    const previewBtn = document.getElementById('preview-btn');
    const sourceBtn = document.getElementById('source-btn');
    
    if (view === 'preview') {
      previewView.classList.remove('hidden');
      sourceView.classList.add('hidden');
      previewBtn.classList.add('bg-indigo-50', 'text-indigo-700', 'border-indigo-300');
      previewBtn.classList.remove('bg-white', 'text-gray-700', 'border-gray-300');
      sourceBtn.classList.remove('bg-indigo-50', 'text-indigo-700', 'border-indigo-300');
      sourceBtn.classList.add('bg-white', 'text-gray-700', 'border-gray-300');
    } else {
      previewView.classList.add('hidden');
      sourceView.classList.remove('hidden');
      sourceBtn.classList.add('bg-indigo-50', 'text-indigo-700', 'border-indigo-300');
      sourceBtn.classList.remove('bg-white', 'text-gray-700', 'border-gray-300');
      previewBtn.classList.remove('bg-indigo-50', 'text-indigo-700', 'border-indigo-300');
      previewBtn.classList.add('bg-white', 'text-gray-700', 'border-gray-300');
    }
  }
  
  // Initialize with preview view
  document.addEventListener('DOMContentLoaded', function() {
    toggleView('preview');
  });
</script>