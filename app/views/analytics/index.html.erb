<div class="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Page header -->
    <div class="bg-white shadow-lg rounded-xl border border-gray-100 p-8 mb-8">
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div class="flex-1 min-w-0">
          <!-- Breadcrumb -->
          <nav class="flex mb-4" aria-label="Breadcrumb">
            <ol role="list" class="flex items-center space-x-4">
              <li>
                <div class="flex">
                  <%= link_to "Dashboard", dashboard_path, class: "text-sm font-medium text-gray-500 hover:text-indigo-600 transition duration-200" %>
                </div>
              </li>
              <li>
                <div class="flex items-center">
                  <svg class="flex-shrink-0 h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                    <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                  </svg>
                  <span class="ml-4 text-sm font-medium text-gray-500">Analytics</span>
                </div>
              </li>
            </ol>
          </nav>
          <!-- Page title & description -->
          <div>
            <h1 class="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-2">
              Analytics
            </h1>
            <p class="text-gray-600 text-lg">
              Track your email campaign performance and engagement metrics.
            </p>
          </div>
        </div>
        <div class="mt-6 lg:mt-0 lg:ml-6 flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3">
          <!-- Date range filter -->
          <%= form_with url: analytics_path, method: :get, local: true, class: "flex items-center space-x-2" do |form| %>
            <%= form.select :date_range, 
                options_for_select([
                  ['Last 7 days', '7_days'],
                  ['Last 30 days', '30_days'],
                  ['Last 90 days', '90_days'],
                  ['This year', 'year']
                ], params[:date_range] || '30_days'),
                {},
                { class: "py-2 px-3 border-gray-300 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition duration-200" } %>
            <%= form.submit "Update", 
                class: "inline-flex items-center px-4 py-2 border border-transparent text-sm leading-4 font-medium rounded-lg text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-200" %>
          <% end %>
          
          <%= link_to analytics_path(format: :csv, date_range: params[:date_range]), 
              class: "inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-200" do %>
            <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Export
          <% end %>
        </div>
      </div>
    </div>

    <!-- Overview Stats with Loading -->
    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8" data-loading-target="content">
      <!-- Total Campaigns -->
      <div class="bg-gradient-to-br from-white to-gray-50 overflow-hidden shadow-lg rounded-xl border border-gray-100 hover:shadow-xl hover:scale-105 transition-all duration-300" data-controller="loading" data-loading-delay-value="500">
        <!-- Loading Skeleton -->
        <div data-loading-target="skeleton" class="p-6 animate-pulse">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-gray-200 rounded-lg"></div>
            </div>
            <div class="ml-4 w-0 flex-1">
              <div class="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div class="h-8 bg-gray-200 rounded w-3/4"></div>
            </div>
          </div>
        </div>

        <!-- Actual Content -->
        <div data-loading-target="content" class="p-6" style="display: none;">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-lg flex items-center justify-center">
                <svg class="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
            </div>
            <div class="ml-4 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-600 truncate">Total Campaigns</dt>
                <dd class="text-2xl font-bold text-gray-900"><%= @overview_stats[:total_campaigns] %></dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <!-- Total Emails Sent -->
      <div class="bg-gradient-to-br from-white to-gray-50 overflow-hidden shadow-lg rounded-xl border border-gray-100 hover:shadow-xl hover:scale-105 transition-all duration-300" data-controller="loading" data-loading-delay-value="700">
        <!-- Loading Skeleton -->
        <div data-loading-target="skeleton" class="p-6 animate-pulse">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-gray-200 rounded-lg"></div>
            </div>
            <div class="ml-4 w-0 flex-1">
              <div class="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div class="h-8 bg-gray-200 rounded w-3/4"></div>
            </div>
          </div>
        </div>

        <!-- Actual Content -->
        <div data-loading-target="content" class="p-6" style="display: none;">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-gradient-to-br from-green-100 to-emerald-100 rounded-lg flex items-center justify-center">
                <svg class="h-6 w-6 text-emerald-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
            </div>
            <div class="ml-4 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-600 truncate">Emails Sent</dt>
                <dd class="text-2xl font-bold text-gray-900"><%= number_with_delimiter(@overview_stats[:total_sent]) %></dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <!-- Average Open Rate -->
      <div class="bg-gradient-to-br from-white to-gray-50 overflow-hidden shadow-lg rounded-xl border border-gray-100 hover:shadow-xl hover:scale-105 transition-all duration-300" data-controller="loading" data-loading-delay-value="900">
        <!-- Loading Skeleton -->
        <div data-loading-target="skeleton" class="p-6 animate-pulse">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-gray-200 rounded-lg"></div>
            </div>
            <div class="ml-4 w-0 flex-1">
              <div class="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div class="h-8 bg-gray-200 rounded w-3/4"></div>
            </div>
          </div>
        </div>

        <!-- Actual Content -->
        <div data-loading-target="content" class="p-6" style="display: none;">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-gradient-to-br from-purple-100 to-pink-100 rounded-lg flex items-center justify-center">
                <svg class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              </div>
            </div>
            <div class="ml-4 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-600 truncate">Avg Open Rate</dt>
                <dd class="text-2xl font-bold text-gray-900"><%= number_to_percentage(@overview_stats[:avg_open_rate], precision: 1) %></dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <!-- Total Contacts -->
      <div class="bg-gradient-to-br from-white to-gray-50 overflow-hidden shadow-lg rounded-xl border border-gray-100 hover:shadow-xl hover:scale-105 transition-all duration-300" data-controller="loading" data-loading-delay-value="1100">
        <!-- Loading Skeleton -->
        <div data-loading-target="skeleton" class="p-6 animate-pulse">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-gray-200 rounded-lg"></div>
            </div>
            <div class="ml-4 w-0 flex-1">
              <div class="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div class="h-8 bg-gray-200 rounded w-3/4"></div>
            </div>
          </div>
        </div>

        <!-- Actual Content -->
        <div data-loading-target="content" class="p-6" style="display: none;">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-gradient-to-br from-orange-100 to-red-100 rounded-lg flex items-center justify-center">
                <svg class="h-6 w-6 text-orange-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
            </div>
            <div class="ml-4 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-600 truncate">Total Contacts</dt>
                <dd class="text-2xl font-bold text-gray-900"><%= number_with_delimiter(@overview_stats[:total_contacts]) %></dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

    <!-- Charts Section -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="grid grid-cols-1 gap-8 lg:grid-cols-2 mb-8">
      <!-- Engagement Trends Chart -->
      <div class="bg-gradient-to-br from-white to-gray-50 shadow-lg rounded-xl border border-gray-100">
        <div class="px-6 py-6">
          <h3 class="text-xl font-bold text-gray-900">Engagement Trends</h3>
          <p class="mt-2 text-sm text-gray-600">Email opens and clicks over time</p>
          <div class="mt-6">
            <div class="h-64 bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl border border-gray-200 p-4">
              <canvas data-controller="chart"
                      data-chart-type-value="line"
                      data-chart-data-value='<%= {
                        labels: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
                        datasets: [{
                          label: "Opens",
                          data: [120, 190, 300, 500, 200, 300, 450],
                          borderColor: "rgb(79, 70, 229)",
                          backgroundColor: "rgba(79, 70, 229, 0.1)",
                          tension: 0.4,
                          fill: true
                        }, {
                          label: "Clicks",
                          data: [60, 95, 150, 250, 100, 150, 225],
                          borderColor: "rgb(168, 85, 247)",
                          backgroundColor: "rgba(168, 85, 247, 0.1)",
                          tension: 0.4,
                          fill: true
                        }]
                      }.to_json %>'
                      data-chart-options-value='<%= {
                        plugins: {
                          legend: {
                            display: true,
                            position: "top"
                          }
                        },
                        scales: {
                          y: {
                            beginAtZero: true,
                            ticks: {
                              callback: "function(value) { return value + ' opens'; }"
                            }
                          }
                        }
                      }.to_json %>'></canvas>
            </div>
          </div>
        </div>
      </div>

      <!-- Campaign Performance -->
      <div class="bg-gradient-to-br from-white to-gray-50 shadow-lg rounded-xl border border-gray-100">
        <div class="px-6 py-6">
          <h3 class="text-xl font-bold text-gray-900">Campaign Performance</h3>
          <p class="mt-2 text-sm text-gray-600">Top performing campaigns by open rate</p>
        <div class="mt-6">
          <% if @campaign_performance.any? %>
            <div class="space-y-4">
              <% @campaign_performance.each_with_index do |campaign, index| %>
                <div class="flex items-center justify-between">
                  <div class="flex-1 min-w-0">
                    <div class="flex items-center">
                      <span class="inline-flex items-center justify-center h-6 w-6 rounded-full bg-indigo-100 text-indigo-800 text-xs font-medium mr-3">
                        <%= index + 1 %>
                      </span>
                      <div class="min-w-0 flex-1">
                        <%= link_to campaign_path(campaign[:id]), class: "text-sm font-medium text-gray-900 hover:text-indigo-600 truncate" do %>
                          <%= campaign[:name] %>
                        <% end %>
                        <p class="text-xs text-gray-500 truncate"><%= campaign[:subject] %></p>
                      </div>
                    </div>
                  </div>
                  <div class="ml-4 flex-shrink-0 text-right">
                    <div class="text-sm font-medium text-gray-900">
                      <%= number_to_percentage(campaign[:open_rate], precision: 1) %>
                    </div>
                    <div class="text-xs text-gray-500">
                      <%= pluralize(campaign[:opens], 'open') %> / <%= pluralize(campaign[:sent], 'sent') %>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          <% else %>
            <div class="text-center py-6">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <p class="mt-2 text-sm text-gray-500">No campaigns found for the selected period.</p>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Detailed Tables -->
  <div class="mt-8 grid grid-cols-1 gap-6 lg:grid-cols-2">
    <!-- Recent Campaign Performance -->
    <div class="bg-gradient-to-br from-white to-gray-50 shadow-lg rounded-xl border border-gray-200 hover:shadow-xl transition-shadow duration-300">
          <div class="px-6 py-6">
            <div class="flex items-center justify-between">
              <h3 class="text-lg leading-6 font-semibold text-gray-900">Recent Campaigns</h3>
              <%= link_to analytics_campaigns_path, class: "text-sm text-indigo-600 hover:text-indigo-700 font-medium transition-colors duration-200" do %>
                View all →
              <% end %>
            </div>
        <div class="mt-6">
              <% if @campaign_performance.any? %>
                <div class="overflow-hidden shadow-md ring-1 ring-gray-200 rounded-lg">
                  <div class="min-w-full overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                      <thead class="bg-gradient-to-r from-gray-50 to-gray-100">
                        <tr>
                          <th class="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Campaign</th>
                          <th class="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Sent</th>
                          <th class="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Opens</th>
                          <th class="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Rate</th>
                        </tr>
                      </thead>
                  <tbody class="bg-white divide-y divide-gray-100">
                        <% @campaign_performance.first(5).each do |campaign_data| %>
                          <tr class="hover:bg-gray-50 transition-colors duration-200">
                            <td class="px-4 py-4 whitespace-nowrap">
                              <div class="text-sm font-medium text-gray-900 truncate max-w-32">
                                <%= link_to campaign_data[:name], campaign_path(campaign_data[:id]), class: "hover:text-indigo-600 transition-colors duration-200" %>
                              </div>
                              <div class="text-xs text-gray-500"><%= time_ago_in_words(campaign_data[:sent_at]) %> ago</div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              <%= number_with_delimiter(campaign_data[:emails_sent]) %>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              <%= number_with_delimiter(campaign_data[:opens]) %>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              <%= number_to_percentage(campaign_data[:open_rate], precision: 1) %>
                            </td>
                          </tr>
                        <% end %>
                      </tbody>
                </table>
              </div>
            </div>
          <% else %>
                  <div class="text-center py-8">
                    <div class="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center">
                      <svg class="w-6 h-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                    <p class="text-sm text-gray-500">No recent campaigns found.</p>
                  </div>
                <% end %>
        </div>
      </div>
    </div>

    <!-- Contact Growth -->
    <div class="bg-gradient-to-br from-white to-gray-50 shadow-lg rounded-xl border border-gray-200 hover:shadow-xl transition-shadow duration-300">
      <div class="px-6 py-6">
        <div class="flex items-center justify-between">
          <h3 class="text-lg leading-6 font-semibold text-gray-900">Contact Growth</h3>
          <%= link_to analytics_contacts_path, class: "text-sm text-indigo-600 hover:text-indigo-700 font-medium transition-colors duration-200" do %>
            View details →
          <% end %>
        </div>
        <div class="mt-6">
          <div class="grid grid-cols-2 gap-4">
            <div class="text-center p-4 bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg border border-green-100">
              <div class="text-2xl font-bold text-green-600"><%= @contact_growth[:new_contacts] %></div>
              <div class="text-sm font-medium text-gray-600">New contacts</div>
            </div>
            <div class="text-center p-4 bg-gradient-to-br from-red-50 to-rose-50 rounded-lg border border-red-100">
              <div class="text-2xl font-bold text-red-600"><%= @contact_growth[:unsubscribed] %></div>
              <div class="text-sm font-medium text-gray-600">Unsubscribed</div>
            </div>
          </div>
          
          <div class="mt-6">
            <div class="text-sm font-semibold text-gray-700 mb-3">Subscription Status</div>
            <div class="space-y-3">
              <div class="flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100">
                <span class="text-sm font-medium text-gray-700">Subscribed</span>
                <span class="text-sm font-bold text-gray-900"><%= number_with_delimiter(@contact_growth[:subscribed]) %></span>
              </div>
              <div class="flex items-center justify-between p-3 bg-gradient-to-r from-gray-50 to-slate-50 rounded-lg border border-gray-200">
                <span class="text-sm font-medium text-gray-700">Unsubscribed</span>
                <span class="text-sm font-bold text-gray-900"><%= number_with_delimiter(@contact_growth[:total_unsubscribed]) %></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>