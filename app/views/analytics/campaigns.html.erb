<div class="min-h-screen bg-gray-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Page header -->
    <div class="bg-white shadow-lg rounded-xl border border-gray-100 p-8 mb-8">
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div class="flex-1 min-w-0">
          <!-- Breadcrumb -->
          <nav class="flex mb-4" aria-label="Breadcrumb">
            <ol role="list" class="flex items-center space-x-4">
              <li>
                <div class="flex">
                  <%= link_to "Dashboard", dashboard_path, class: "text-sm font-medium text-gray-500 hover:text-indigo-600 transition duration-200" %>
                </div>
              </li>
              <li>
                <div class="flex items-center">
                  <svg class="flex-shrink-0 h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                    <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                  </svg>
                  <%= link_to "Analytics", analytics_path, class: "ml-4 text-sm font-medium text-gray-500 hover:text-indigo-600 transition duration-200" %>
                </div>
              </li>
              <li>
                <div class="flex items-center">
                  <svg class="flex-shrink-0 h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                    <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                  </svg>
                  <span class="ml-4 text-sm font-medium text-gray-500">Campaigns</span>
                </div>
              </li>
            </ol>
          </nav>
          <!-- Page title & description -->
          <div>
            <h1 class="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-2">
              Campaign Analytics 📊
            </h1>
            <p class="text-gray-600 text-lg">
              Detailed performance metrics for all your email campaigns.
            </p>
          </div>
        </div>
        <div class="mt-6 lg:mt-0 lg:ml-6 flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3">
          <!-- Period filter -->
          <%= form_with url: campaigns_analytics_path, method: :get, local: true, class: "flex items-center space-x-2" do |form| %>
            <%= form.select :period, 
                options_for_select([
                  ['Last 7 days', 'week'],
                  ['Last 30 days', 'month'],
                  ['Last 3 months', '3months'],
                  ['This year', 'year'],
                  ['Custom range', 'custom']
                ], params[:period] || 'month'),
                {},
                { class: "py-2 px-3 border-gray-300 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm transition duration-200" } %>
            <%= form.submit "Update", 
                class: "inline-flex items-center px-4 py-2 border border-transparent text-sm leading-4 font-medium rounded-lg text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-200" %>
          <% end %>
          
          <!-- Export button -->
          <%= link_to export_analytics_path(format: 'csv', period: params[:period] || 'month'), 
              class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-200" do %>
            <svg class="-ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Export CSV
          <% end %>
        </div>
      </div>
    </div>

    <!-- Campaign Performance Overview -->
    <% if @campaigns_with_stats.any? %>
      <div class="grid grid-cols-1 gap-6 mb-8">
        <!-- Summary Stats -->
        <div class="bg-white shadow-lg rounded-xl border border-gray-100 p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-6">Campaign Performance Summary</h2>
          <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <% 
              total_campaigns = @campaigns_with_stats.length
              total_sent = @campaigns_with_stats.sum { |c| c[:stats][:total_sent] }
              total_opened = @campaigns_with_stats.sum { |c| c[:stats][:total_opened] }
              total_clicked = @campaigns_with_stats.sum { |c| c[:stats][:total_clicked] }
              avg_open_rate = total_sent > 0 ? (total_opened.to_f / total_sent * 100).round(2) : 0
              avg_click_rate = total_sent > 0 ? (total_clicked.to_f / total_sent * 100).round(2) : 0
            %>
            
            <div class="text-center">
              <div class="text-3xl font-bold text-indigo-600"><%= total_campaigns %></div>
              <div class="text-sm text-gray-500 mt-1">Total Campaigns</div>
            </div>
            
            <div class="text-center">
              <div class="text-3xl font-bold text-green-600"><%= number_with_delimiter(total_sent) %></div>
              <div class="text-sm text-gray-500 mt-1">Emails Sent</div>
            </div>
            
            <div class="text-center">
              <div class="text-3xl font-bold text-blue-600"><%= avg_open_rate %>%</div>
              <div class="text-sm text-gray-500 mt-1">Avg Open Rate</div>
            </div>
            
            <div class="text-center">
              <div class="text-3xl font-bold text-purple-600"><%= avg_click_rate %>%</div>
              <div class="text-sm text-gray-500 mt-1">Avg Click Rate</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Campaign List -->
      <div class="bg-white shadow-lg rounded-xl border border-gray-100 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
          <h2 class="text-xl font-semibold text-gray-900">Campaign Details</h2>
        </div>
        
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Campaign
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Sent
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Opened
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Clicked
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Open Rate
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Click Rate
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Created
                </th>
                <th scope="col" class="relative px-6 py-3">
                  <span class="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <% @campaigns_with_stats.each do |campaign_data| %>
                <% campaign = campaign_data[:campaign] %>
                <% stats = campaign_data[:stats] %>
                <tr class="hover:bg-gray-50 transition-colors duration-200">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-10 w-10">
                        <div class="h-10 w-10 rounded-full bg-gradient-to-r from-indigo-500 to-purple-600 flex items-center justify-center">
                          <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                          </svg>
                        </div>
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">
                          <%= link_to campaign.name, campaign_path(campaign), class: "hover:text-indigo-600 transition-colors duration-200" %>
                        </div>
                        <div class="text-sm text-gray-500">
                          <%= truncate(campaign.subject, length: 50) %>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                      <%= case campaign.status
                          when 'sent' then 'bg-green-100 text-green-800'
                          when 'draft' then 'bg-gray-100 text-gray-800'
                          when 'scheduled' then 'bg-blue-100 text-blue-800'
                          else 'bg-yellow-100 text-yellow-800'
                          end %>">
                      <%= campaign.status.humanize %>
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <%= number_with_delimiter(stats[:total_sent]) %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <%= number_with_delimiter(stats[:total_opened]) %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <%= number_with_delimiter(stats[:total_clicked]) %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="text-sm font-medium text-gray-900"><%= stats[:open_rate] %>%</div>
                      <div class="ml-2 w-16 bg-gray-200 rounded-full h-2">
                        <div class="bg-blue-600 h-2 rounded-full" style="width: <%= [stats[:open_rate], 100].min %>%"></div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="text-sm font-medium text-gray-900"><%= stats[:click_rate] %>%</div>
                      <div class="ml-2 w-16 bg-gray-200 rounded-full h-2">
                        <div class="bg-purple-600 h-2 rounded-full" style="width: <%= [stats[:click_rate], 100].min %>%"></div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <%= campaign.created_at.strftime("%b %d, %Y") %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <%= link_to "View", campaign_path(campaign), 
                        class: "text-indigo-600 hover:text-indigo-900 transition-colors duration-200" %>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      </div>
    <% else %>
      <!-- Empty State -->
      <div class="bg-white shadow-lg rounded-xl border border-gray-100 p-12 text-center">
        <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2-2V7a2 2 0 012-2h2a2 2 0 002 2v2a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 00-2 2h-2a2 2 0 00-2 2v6a2 2 0 01-2 2H9z" />
        </svg>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No campaigns found</h3>
        <p class="text-gray-500 mb-6">You haven't created any campaigns in the selected time period.</p>
        <%= link_to "Create Your First Campaign", new_campaign_path, 
            class: "inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-200" %>
      </div>
    <% end %>
  </div>
</div>