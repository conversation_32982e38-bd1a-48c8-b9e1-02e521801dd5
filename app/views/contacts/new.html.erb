<% content_for :title, "New Contact - RapidMarkt" %>
<!-- Enhanced Background -->
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
  <div class="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8 py-8">
    <!-- Enhanced Breadcrumb -->
    <nav class="flex mb-8" aria-label="Breadcrumb">
      <ol role="list" class="flex items-center space-x-4 bg-white/70 backdrop-blur-sm rounded-full px-6 py-3 shadow-sm border border-white/20">
        <li>
          <div>
            <%= link_to dashboard_path, class: "text-gray-500 hover:text-indigo-600 transition-colors duration-200" do %>
              <svg class="flex-shrink-0 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
              </svg>
              <span class="sr-only">Home</span>
            <% end %>
          </div>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="flex-shrink-0 h-4 w-4 text-gray-300" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
            <%= link_to "Contacts", contacts_path, class: "ml-3 text-sm font-medium text-gray-600 hover:text-indigo-600 transition-colors duration-200" %>
          </div>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="flex-shrink-0 h-4 w-4 text-gray-300" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
            <span class="ml-3 text-sm font-medium text-indigo-600">Add Contact</span>
          </div>
        </li>
      </ol>
    </nav>

    <!-- Enhanced Page Header -->
    <div class="text-center mb-12">
      <div class="mx-auto w-20 h-20 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6 shadow-lg">
        <svg class="h-10 w-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
        </svg>
      </div>
      <h1 class="text-4xl font-bold text-gray-900 mb-4">
        Add New Contact
      </h1>
      <p class="text-lg text-gray-600 max-w-2xl mx-auto">
        Expand your network by adding a new contact to your email list. Organize with tags for precise targeting and better engagement.
      </p>
    </div>

    <!-- Enhanced Form Container -->
    <div class="bg-white/80 backdrop-blur-sm shadow-2xl rounded-3xl border border-white/20 overflow-hidden">
      <div class="px-8 py-10">
        <%= render 'form', contact: @contact %>
      </div>
    </div>

    <!-- Quick Tips Section -->
    <div class="mt-8 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-200/50">
      <div class="flex items-start space-x-4">
        <div class="flex-shrink-0">
          <svg class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <div>
          <h3 class="text-lg font-semibold text-blue-900 mb-2">Pro Tips</h3>
          <ul class="text-sm text-blue-800 space-y-1">
            <li>• Use descriptive tags to segment your audience effectively</li>
            <li>• Double-check email addresses to ensure deliverability</li>
            <li>• Consider the contact's subscription preferences</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>