<div class="min-h-screen bg-gray-50">
  <!-- Header Section -->
  <div class="bg-white shadow-sm border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="py-6">
        <div class="md:flex md:items-center md:justify-between">
          <div class="flex-1 min-w-0">
            <h1 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
              Import Contacts 📥
            </h1>
            <p class="mt-1 text-sm text-gray-500">
              Upload a CSV file to import multiple contacts at once.
            </p>
          </div>
          <div class="mt-4 flex md:mt-0 md:ml-4">
            <%= link_to contacts_path, 
                class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-200" do %>
              <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              Back to Contacts
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    
    <!-- Import Form -->
    <div class="bg-white shadow-lg rounded-xl border border-gray-100 hover:shadow-xl transition-shadow duration-200">
      <div class="px-6 py-8">
        <%= form_with url: import_contacts_path, method: :post, multipart: true, local: true, class: "space-y-6" do |form| %>
          
          <!-- File Upload Section -->
          <div>
            <%= form.label :file, "Choose CSV File", class: "block text-sm font-medium text-gray-700 mb-2" %>
            <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-lg hover:border-indigo-400 transition-colors duration-200">
              <div class="space-y-1 text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                  <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
                <div class="flex text-sm text-gray-600">
                  <label for="file" class="relative cursor-pointer bg-white rounded-md font-medium text-indigo-600 hover:text-indigo-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500">
                    <span>Upload a file</span>
                    <%= form.file_field :file, id: "file", accept: ".csv", class: "sr-only" %>
                  </label>
                  <p class="pl-1">or drag and drop</p>
                </div>
                <p class="text-xs text-gray-500">
                  CSV files only
                </p>
              </div>
            </div>
          </div>

          <!-- Submit Button -->
          <div class="flex justify-end">
            <%= form.submit "Import Contacts", 
                class: "inline-flex items-center px-6 py-3 border border-transparent rounded-lg shadow-sm text-base font-medium text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-200" %>
          </div>
        <% end %>
      </div>
    </div>

    <!-- Instructions -->
    <div class="mt-8 bg-blue-50 border border-blue-200 rounded-xl p-6">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-blue-800">
            CSV Format Requirements
          </h3>
          <div class="mt-2 text-sm text-blue-700">
            <p class="mb-2">Your CSV file should include the following columns:</p>
            <ul class="list-disc list-inside space-y-1">
              <li><strong>first_name</strong> - Contact's first name</li>
              <li><strong>last_name</strong> - Contact's last name</li>
              <li><strong>email</strong> - Contact's email address (required)</li>
              <li><strong>phone</strong> - Contact's phone number (optional)</li>
              <li><strong>company</strong> - Contact's company (optional)</li>
              <li><strong>status</strong> - Contact status: active, inactive, or bounced (optional, defaults to active)</li>
            </ul>
            <p class="mt-3 text-xs">
              <strong>Note:</strong> The first row should contain column headers. Duplicate emails will be skipped.
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Sample CSV Download -->
    <div class="mt-6 text-center">
      <p class="text-sm text-gray-600 mb-2">Need a template?</p>
      <a href="data:text/csv;charset=utf-8,first_name,last_name,email,phone,company,status%0AJohn,Doe,<EMAIL>,555-1234,Acme Corp,active%0AJane,Smith,<EMAIL>,555-5678,Tech Inc,active" 
         download="contacts_template.csv"
         class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-200">
        <svg class="-ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        Download Sample CSV
      </a>
    </div>
  </div>
</div>