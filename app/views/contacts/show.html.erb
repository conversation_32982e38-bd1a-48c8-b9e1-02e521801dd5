<!-- Enhanced Background -->
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
  <div class="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8 py-8">
    <!-- Enhanced Breadcrumb -->
    <nav class="flex mb-8" aria-label="Breadcrumb">
      <ol role="list" class="flex items-center space-x-4 bg-white/70 backdrop-blur-sm rounded-full px-6 py-3 shadow-sm border border-white/20">
        <li>
          <div>
            <%= link_to dashboard_path, class: "text-gray-500 hover:text-indigo-600 transition-colors duration-200" do %>
              <svg class="flex-shrink-0 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
              </svg>
              <span class="sr-only">Home</span>
            <% end %>
          </div>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="flex-shrink-0 h-4 w-4 text-gray-300" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
            <%= link_to "Contacts", contacts_path, class: "ml-3 text-sm font-medium text-gray-600 hover:text-indigo-600 transition-colors duration-200" %>
          </div>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="flex-shrink-0 h-4 w-4 text-gray-300" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
            <span class="ml-3 text-sm font-medium text-indigo-600"><%= @contact.full_name %></span>
          </div>
        </li>
      </ol>
    </nav>

    <!-- Enhanced Contact Header -->
    <div class="bg-white/80 backdrop-blur-sm shadow-2xl rounded-3xl border border-white/20 overflow-hidden mb-8">
      <div class="px-8 py-10">
        <div class="flex items-center space-x-6">
          <!-- Contact Avatar -->
          <div class="flex-shrink-0">
            <div class="w-24 h-24 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg">
              <div class="text-3xl font-bold text-white">
                <%= @contact.full_name.first.upcase %>
              </div>
            </div>
          </div>
          
          <!-- Contact Info -->
          <div class="flex-1 min-w-0">
            <h1 class="text-4xl font-bold text-gray-900 mb-2">
              <%= @contact.full_name %>
            </h1>
            <div class="flex flex-wrap items-center gap-4 mb-4">
              <div class="flex items-center text-gray-600">
                <svg class="flex-shrink-0 mr-2 h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                  <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                </svg>
                <span class="text-lg"><%= @contact.email %></span>
              </div>
              <div class="flex items-center">
                <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold <%= @contact.status == 'subscribed' ? 'bg-green-100 text-green-800 border border-green-200' : 'bg-red-100 text-red-800 border border-red-200' %>">
                  <% if @contact.status == 'subscribed' %>
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                  <% else %>
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M13.477 14.89A6 6 0 015.11 6.524l8.367 8.368zm1.414-1.414L6.524 5.11a6 6 0 018.367 8.367zM18 10a8 8 0 11-16 0 8 8 0 0116 0z" clip-rule="evenodd" />
                    </svg>
                  <% end %>
                  <%= @contact.status.humanize %>
                </span>
              </div>
            </div>
            
            <!-- Quick Stats -->
            <div class="grid grid-cols-3 gap-4">
              <div class="text-center p-3 bg-blue-50 rounded-xl">
                <div class="text-2xl font-bold text-blue-600"><%= @contact.emails_received || 0 %></div>
                <div class="text-sm text-blue-700">Emails Received</div>
              </div>
              <div class="text-center p-3 bg-green-50 rounded-xl">
                <div class="text-2xl font-bold text-green-600"><%= @contact.emails_opened || 0 %></div>
                <div class="text-sm text-green-700">Emails Opened</div>
              </div>
              <div class="text-center p-3 bg-purple-50 rounded-xl">
                <div class="text-2xl font-bold text-purple-600"><%= @contact.tags.count %></div>
                <div class="text-sm text-purple-700">Tags</div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="mt-8 flex flex-wrap gap-3">
          <%= link_to edit_contact_path(@contact), 
              class: "inline-flex items-center px-6 py-3 border border-gray-300 rounded-xl shadow-sm text-sm font-medium text-gray-700 bg-white/80 backdrop-blur-sm hover:bg-white hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200" do %>
            <svg class="-ml-1 mr-2 h-5 w-5 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
              <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
            </svg>
            Edit Contact
          <% end %>
          
          <% if @contact.status == 'subscribed' %>
            <%= link_to contact_path(@contact, contact: { status: 'unsubscribed' }), 
                data: { turbo_method: :patch, turbo_confirm: 'Are you sure you want to unsubscribe this contact?' },
                class: "inline-flex items-center px-6 py-3 border border-orange-300 rounded-xl shadow-sm text-sm font-medium text-orange-700 bg-orange-50/80 backdrop-blur-sm hover:bg-orange-100 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-all duration-200" do %>
              <svg class="-ml-1 mr-2 h-5 w-5 text-orange-500" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M13.477 14.89A6 6 0 015.11 6.524l8.367 8.368zm1.414-1.414L6.524 5.11a6 6 0 018.367 8.367zM18 10a8 8 0 11-16 0 8 8 0 0116 0z" clip-rule="evenodd" />
              </svg>
              Unsubscribe
            <% end %>
          <% else %>
            <%= link_to contact_path(@contact, contact: { status: 'subscribed' }), 
                data: { turbo_method: :patch },
                class: "inline-flex items-center px-6 py-3 border border-transparent rounded-xl shadow-sm text-sm font-medium text-white bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200 transform hover:scale-105" do %>
              <svg class="-ml-1 mr-2 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              Resubscribe
            <% end %>
          <% end %>
          
          <%= link_to contacts_path, 
              class: "inline-flex items-center px-6 py-3 border border-indigo-300 rounded-xl shadow-sm text-sm font-medium text-indigo-700 bg-indigo-50/80 backdrop-blur-sm hover:bg-indigo-100 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200" do %>
            <svg class="-ml-1 mr-2 h-5 w-5 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
            </svg>
            Back to Contacts
          <% end %>
          
          <%= link_to contact_path(@contact), 
              data: { turbo_method: :delete, turbo_confirm: 'Are you sure you want to delete this contact? This action cannot be undone.' },
              class: "inline-flex items-center px-6 py-3 border border-transparent rounded-xl shadow-sm text-sm font-medium text-white bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-700 hover:to-pink-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200 transform hover:scale-105" do %>
            <svg class="-ml-1 mr-2 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" clip-rule="evenodd" />
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414L7.586 12l-1.293 1.293a1 1 0 101.414 1.414L9 13.414l2.293 2.293a1 1 0 001.414-1.414L11.414 12l1.293-1.293z" clip-rule="evenodd" />
            </svg>
            Delete Contact
          <% end %>
        </div>
      </div>
    </div>

    <!-- Contact Details Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- Contact Information Card -->
      <div class="bg-white/80 backdrop-blur-sm shadow-xl rounded-2xl border border-white/20 overflow-hidden">
        <div class="px-6 py-6 border-b border-gray-100">
          <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
              <svg class="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
            <div>
              <h3 class="text-xl font-semibold text-gray-900">Contact Information</h3>
              <p class="text-sm text-gray-600">Personal details and contact data</p>
            </div>
          </div>
        </div>
        <div class="p-6 space-y-6">
          <div class="flex items-center justify-between py-3 border-b border-gray-100">
            <div class="flex items-center space-x-3">
              <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              <span class="text-sm font-medium text-gray-600">Full Name</span>
            </div>
            <span class="text-sm font-semibold text-gray-900"><%= @contact.full_name %></span>
          </div>
          
          <div class="flex items-center justify-between py-3 border-b border-gray-100">
            <div class="flex items-center space-x-3">
              <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              <span class="text-sm font-medium text-gray-600">Email</span>
            </div>
            <span class="text-sm font-semibold text-gray-900"><%= @contact.email %></span>
          </div>
          
          <div class="flex items-center justify-between py-3 border-b border-gray-100">
            <div class="flex items-center space-x-3">
              <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span class="text-sm font-medium text-gray-600">Status</span>
            </div>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold <%= @contact.status == 'subscribed' ? 'bg-green-100 text-green-800 border border-green-200' : 'bg-red-100 text-red-800 border border-red-200' %>">
              <% if @contact.status == 'subscribed' %>
                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
              <% else %>
                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M13.477 14.89A6 6 0 015.11 6.524l8.367 8.368zm1.414-1.414L6.524 5.11a6 6 0 018.367 8.367zM18 10a8 8 0 11-16 0 8 8 0 0116 0z" clip-rule="evenodd" />
                </svg>
              <% end %>
              <%= @contact.status.humanize %>
            </span>
          </div>
          
          <div class="flex items-center justify-between py-3 border-b border-gray-100">
            <div class="flex items-center space-x-3">
              <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              <span class="text-sm font-medium text-gray-600">Added</span>
            </div>
            <span class="text-sm text-gray-900"><%= @contact.created_at.strftime("%b %d, %Y") %></span>
          </div>
          
          <div class="flex items-center justify-between py-3 border-b border-gray-100">
            <div class="flex items-center space-x-3">
              <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              <span class="text-sm font-medium text-gray-600">Last Updated</span>
            </div>
            <span class="text-sm text-gray-900"><%= @contact.updated_at.strftime("%b %d, %Y") %></span>
          </div>
          
          <div class="pt-3">
            <div class="flex items-start space-x-3 mb-3">
              <svg class="h-5 w-5 text-gray-400 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
              </svg>
              <span class="text-sm font-medium text-gray-600">Tags</span>
            </div>
            <% if @contact.tags.any? %>
              <div class="flex flex-wrap gap-2">
                <% @contact.tags.each do |tag| %>
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 border border-blue-200">
                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M17.707 9.293a1 1 0 010 1.414l-7 7a1 1 0 01-1.414 0l-7-7A.997.997 0 012 10V5a3 3 0 013-3h5c.256 0 .512.098.707.293l7 7zM5 6a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                    </svg>
                    <%= tag.name %>
                  </span>
                <% end %>
              </div>
            <% else %>
              <div class="text-center py-4">
                <svg class="mx-auto h-8 w-8 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                </svg>
                <p class="text-sm text-gray-500 mt-2">No tags assigned</p>
              </div>
            <% end %>
          </div>
        </div>
      </div>
      
      <!-- Campaign Statistics Card -->
      <div class="bg-white/80 backdrop-blur-sm shadow-xl rounded-2xl border border-white/20 overflow-hidden">
        <div class="px-6 py-6 border-b border-gray-100">
          <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
              <svg class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <div>
              <h3 class="text-xl font-semibold text-gray-900">Email Engagement</h3>
              <p class="text-sm text-gray-600">Campaign performance and activity metrics</p>
            </div>
          </div>
        </div>
        <div class="p-6">
          <!-- Email Stats Grid -->
          <div class="grid grid-cols-2 gap-6 mb-6">
            <div class="text-center p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl border border-blue-200">
              <div class="text-3xl font-bold text-blue-600 mb-1"><%= @contact.emails_received || 0 %></div>
              <div class="text-sm font-medium text-blue-700">Emails Received</div>
            </div>
            <div class="text-center p-4 bg-gradient-to-br from-green-50 to-green-100 rounded-xl border border-green-200">
              <div class="text-3xl font-bold text-green-600 mb-1"><%= @contact.emails_opened || 0 %></div>
              <div class="text-sm font-medium text-green-700">Emails Opened</div>
            </div>
          </div>
          
          <!-- Engagement Rate -->
          <div class="mb-6">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm font-medium text-gray-700">Open Rate</span>
              <% 
                emails_received = @contact.emails_received || 0
                emails_opened = @contact.emails_opened || 0
                open_rate = emails_received > 0 ? (emails_opened.to_f / emails_received * 100).round(1) : 0
              %>
              <span class="text-sm font-semibold text-gray-900"><%= open_rate %>%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-3">
              <div class="bg-gradient-to-r from-green-400 to-green-600 h-3 rounded-full transition-all duration-500" style="width: <%= open_rate %>%"></div>
            </div>
          </div>
          
          <!-- Additional Stats -->
          <div class="space-y-4">
            <div class="flex items-center justify-between py-3 border-b border-gray-100">
              <div class="flex items-center space-x-3">
                <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                </svg>
                <span class="text-sm font-medium text-gray-600">Links Clicked</span>
              </div>
              <span class="text-lg font-semibold text-gray-900"><%= @contact.links_clicked || 0 %></span>
            </div>
            
            <div class="flex items-center justify-between py-3">
              <div class="flex items-center space-x-3">
                <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span class="text-sm font-medium text-gray-600">Last Activity</span>
              </div>
              <span class="text-sm text-gray-900">
                <%= @contact.last_opened_at ? @contact.last_opened_at.strftime("%b %d, %Y") : "Never" %>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>