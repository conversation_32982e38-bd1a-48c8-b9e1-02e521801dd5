<!-- Enhanced Background -->
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
  <div class="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8 py-8">
    <!-- Enhanced Breadcrumb -->
    <nav class="flex mb-8" aria-label="Breadcrumb">
      <ol role="list" class="flex items-center space-x-4 bg-white/70 backdrop-blur-sm rounded-full px-6 py-3 shadow-sm border border-white/20">
        <li>
          <div>
            <%= link_to dashboard_path, class: "text-gray-500 hover:text-indigo-600 transition-colors duration-200" do %>
              <svg class="flex-shrink-0 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
              </svg>
              <span class="sr-only">Home</span>
            <% end %>
          </div>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="flex-shrink-0 h-4 w-4 text-gray-300" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
            <%= link_to "Contacts", contacts_path, class: "ml-3 text-sm font-medium text-gray-600 hover:text-indigo-600 transition-colors duration-200" %>
          </div>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="flex-shrink-0 h-4 w-4 text-gray-300" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
            <%= link_to @contact.full_name, contact_path(@contact), class: "ml-3 text-sm font-medium text-gray-600 hover:text-indigo-600 transition-colors duration-200" %>
          </div>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="flex-shrink-0 h-4 w-4 text-gray-300" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
            <span class="ml-3 text-sm font-medium text-indigo-600">Edit</span>
          </div>
        </li>
      </ol>
    </nav>

    <!-- Enhanced Page Header -->
    <div class="text-center mb-12">
      <div class="mx-auto w-20 h-20 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mb-6 shadow-lg">
        <div class="text-2xl font-bold text-white">
          <%= @contact.full_name.first.upcase %>
        </div>
      </div>
      <h1 class="text-4xl font-bold text-gray-900 mb-2">
        Edit <%= @contact.full_name %>
      </h1>
      <p class="text-lg text-gray-600 mb-6">
        Update contact information and manage tags for better organization.
      </p>
      
      <!-- Quick Actions -->
      <div class="flex justify-center space-x-4">
        <%= link_to contact_path(@contact), 
            class: "inline-flex items-center px-6 py-3 border border-gray-300 rounded-xl shadow-sm text-sm font-medium text-gray-700 bg-white/80 backdrop-blur-sm hover:bg-white hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200" do %>
          <svg class="-ml-1 mr-2 h-5 w-5 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
            <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
          </svg>
          View Contact
        <% end %>
        
        <%= link_to contacts_path, 
            class: "inline-flex items-center px-6 py-3 border border-indigo-300 rounded-xl shadow-sm text-sm font-medium text-indigo-700 bg-indigo-50/80 backdrop-blur-sm hover:bg-indigo-100 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200" do %>
          <svg class="-ml-1 mr-2 h-5 w-5 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
          </svg>
          Back to Contacts
        <% end %>
      </div>
    </div>

    <!-- Enhanced Form Container -->
    <div class="bg-white/80 backdrop-blur-sm shadow-2xl rounded-3xl border border-white/20 overflow-hidden">
      <div class="px-8 py-10">
        <%= render 'form', contact: @contact %>
      </div>
    </div>

    <!-- Contact Stats Section -->
    <div class="mt-8 bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-6 border border-green-200/50">
      <div class="flex items-start space-x-4">
        <div class="flex-shrink-0">
          <svg class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
        </div>
        <div class="flex-1">
          <h3 class="text-lg font-semibold text-green-900 mb-2">Contact Statistics</h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div class="bg-white/60 rounded-lg p-3">
              <div class="text-2xl font-bold text-blue-600"><%= @contact.campaign_contacts.where.not(sent_at: nil).count %></div>
              <div class="text-green-700">Emails Received</div>
            </div>
            <div class="bg-white/60 rounded-lg p-3">
              <div class="text-2xl font-bold text-green-600"><%= @contact.campaign_contacts.where.not(opened_at: nil).count %></div>
              <div class="text-green-700">Emails Opened</div>
            </div>
            <div class="bg-white/60 rounded-lg p-3">
              <div class="text-2xl font-bold text-purple-600"><%= @contact.tags.count %></div>
              <div class="text-green-700">Tags Applied</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>