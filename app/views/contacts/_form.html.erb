
<%= form_with(model: contact, local: true, class: "space-y-8") do |form| %>
  <!-- Enhanced Error Messages -->
  <% if contact.errors.any? %>
    <div class="rounded-2xl bg-red-50 border border-red-200 p-6 mb-8">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-6 w-6 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <div class="ml-4">
          <h3 class="text-lg font-semibold text-red-800 mb-2">
            Please fix the following <%= pluralize(contact.errors.count, "error") %>:
          </h3>
          <div class="text-sm text-red-700">
            <ul role="list" class="list-disc pl-5 space-y-1">
              <% contact.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <!-- Contact Information Section -->
  <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-8 border border-blue-200/50">
    <div class="flex items-center mb-6">
      <div class="flex-shrink-0">
        <svg class="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
      </div>
      <div class="ml-4">
        <h3 class="text-xl font-bold text-gray-900">Contact Information</h3>
        <p class="text-sm text-gray-600">
          Enter the basic details for this contact.
        </p>
      </div>
    </div>

    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
      <div>
        <%= form.label :first_name, class: "block text-sm font-semibold text-gray-700 mb-2" %>
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>
          <%= form.text_field :first_name, 
              placeholder: "Enter first name",
              class: "pl-10 block w-full rounded-xl border-gray-300 bg-white/80 backdrop-blur-sm shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-all duration-200" %>
        </div>
      </div>

      <div>
        <%= form.label :last_name, class: "block text-sm font-semibold text-gray-700 mb-2" %>
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>
          <%= form.text_field :last_name, 
              placeholder: "Enter last name",
              class: "pl-10 block w-full rounded-xl border-gray-300 bg-white/80 backdrop-blur-sm shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-all duration-200" %>
        </div>
      </div>

      <div>
        <%= form.label :email, class: "block text-sm font-semibold text-gray-700 mb-2" %>
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
            </svg>
          </div>
          <%= form.email_field :email, 
              placeholder: "Enter email address",
              class: "pl-10 block w-full rounded-xl border-gray-300 bg-white/80 backdrop-blur-sm shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-all duration-200" %>
        </div>
      </div>

      <div>
        <%= form.label :status, class: "block text-sm font-semibold text-gray-700 mb-2" %>
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <%= form.select :status, 
              options_for_select([
                ['Subscribed', 'subscribed'],
                ['Unsubscribed', 'unsubscribed']
              ], contact.status),
              { prompt: 'Select status' },
              { class: "pl-10 block w-full rounded-xl border-gray-300 bg-white/80 backdrop-blur-sm shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-all duration-200" } %>
        </div>
      </div>
    </div>
  </div>

  <!-- Tags Section -->
  <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl p-8 border border-purple-200/50">
    <div class="flex items-center mb-6">
      <div class="flex-shrink-0">
        <svg class="h-8 w-8 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
        </svg>
      </div>
      <div class="ml-4">
        <h3 class="text-xl font-bold text-gray-900">Tags & Organization</h3>
        <p class="text-sm text-gray-600">
          Organize your contact with relevant tags for better management.
        </p>
      </div>
    </div>

    <!-- Existing Tags -->
    <div class="mb-8">
      <h4 class="text-lg font-semibold text-gray-800 mb-4">Select Existing Tags</h4>
      <% if @current_account.tags.any? %>
        <div class="grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3">
          <% @current_account.tags.order(:name).each do |tag| %>
            <label for="tag_<%= tag.id %>" class="relative flex items-center p-4 bg-white/80 backdrop-blur-sm rounded-xl border border-gray-200 cursor-pointer hover:bg-white hover:shadow-md transition-all duration-200 group">
              <div class="flex items-center h-5">
                <%= check_box_tag "contact[tag_ids][]", tag.id, contact.tag_ids.include?(tag.id), 
                    { id: "tag_#{tag.id}", class: "focus:ring-purple-500 h-4 w-4 text-purple-600 border-gray-300 rounded transition-colors duration-200" } %>
              </div>
              <div class="ml-3 flex-1">
                <span class="text-sm font-medium text-gray-700 group-hover:text-gray-900"><%= tag.name %></span>
                <div class="text-xs text-gray-500"><%= pluralize(tag.contacts.count, 'contact') %></div>
              </div>
              <div class="ml-2">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                  <%= tag.contacts.count %>
                </span>
              </div>
            </label>
          <% end %>
        </div>
      <% else %>
        <div class="text-center py-8 bg-white/60 rounded-xl border border-dashed border-gray-300">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
          </svg>
          <p class="mt-2 text-sm text-gray-500">No tags available yet.</p>
          <p class="text-xs text-gray-400">Create some tags below to organize your contacts.</p>
        </div>
      <% end %>
    </div>

    <!-- Add New Tags -->
    <div>
      <h4 class="text-lg font-semibold text-gray-800 mb-4">Create New Tags</h4>
      <div class="relative">
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
        </div>
        <%= form.text_field :new_tags, 
            placeholder: "e.g., VIP Customer, Newsletter Subscriber, Premium Member",
            class: "pl-10 block w-full rounded-xl border-gray-300 bg-white/80 backdrop-blur-sm shadow-sm focus:border-purple-500 focus:ring-purple-500 sm:text-sm transition-all duration-200" %>
      </div>
      <div class="mt-3 flex items-start space-x-2">
        <svg class="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <p class="text-sm text-gray-600">
          <strong>Tip:</strong> Separate multiple tags with commas. New tags will be created automatically and can be reused for other contacts.
        </p>
      </div>
    </div>
  </div>

  <!-- Enhanced Action Buttons -->
  <div class="flex justify-end space-x-4 pt-6">
    <%= link_to "Cancel", contacts_path, 
        class: "inline-flex items-center px-6 py-3 border border-gray-300 rounded-xl shadow-sm text-sm font-medium text-gray-700 bg-white/80 backdrop-blur-sm hover:bg-white hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200" %>
    <%= form.submit contact.persisted? ? "Update Contact" : "Add Contact", 
        class: "inline-flex items-center px-8 py-3 border border-transparent rounded-xl shadow-sm text-sm font-medium text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 transform hover:scale-105" %>
  </div>
<% end %>
