<% content_for :title, "Contacts - RapidMarkt" %>

<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
  <!-- Enhanced Header Section -->
  <div class="bg-white/80 backdrop-blur-sm shadow-lg border-b border-gray-200/50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="py-8">
        <div class="md:flex md:items-center md:justify-between">
          <div class="flex-1 min-w-0">
            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                  <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
              </div>
              <div>
                <h1 class="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent sm:text-4xl">
                  Contact Management
                </h1>
                <p class="mt-2 text-lg text-gray-600">
                  Build, organize, and engage with your audience effectively
                </p>
              </div>
            </div>
            
            <!-- Quick Stats -->
            <div class="mt-6 grid grid-cols-1 gap-4 sm:grid-cols-4">
              <div class="bg-white/60 backdrop-blur-sm rounded-xl p-4 border border-gray-200/50">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                      <svg class="w-4 h-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                      </svg>
                    </div>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">Total Contacts</p>
                    <p class="text-2xl font-bold text-gray-900"><%= @contacts.respond_to?(:total_count) ? @contacts.total_count : @contacts.count %></p>
                  </div>
                </div>
              </div>
              
              <div class="bg-white/60 backdrop-blur-sm rounded-xl p-4 border border-gray-200/50">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                      <svg class="w-4 h-4 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">Subscribed</p>
                    <p class="text-2xl font-bold text-green-600"><%= @current_account.contacts.where(status: 'subscribed').count %></p>
                  </div>
                </div>
              </div>
              
              <div class="bg-white/60 backdrop-blur-sm rounded-xl p-4 border border-gray-200/50">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                      <svg class="w-4 h-4 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">Unsubscribed</p>
                    <p class="text-2xl font-bold text-red-600"><%= @current_account.contacts.where(status: 'unsubscribed').count %></p>
                  </div>
                </div>
              </div>
              
              <div class="bg-white/60 backdrop-blur-sm rounded-xl p-4 border border-gray-200/50">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                      <svg class="w-4 h-4 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                      </svg>
                    </div>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">Tags</p>
                    <p class="text-2xl font-bold text-purple-600"><%= @current_account.tags.count %></p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="mt-6 flex md:mt-0 md:ml-6 space-x-3">
            <%= link_to export_contacts_path(format: :csv), 
                class: "inline-flex items-center px-5 py-3 border border-gray-300 rounded-xl shadow-sm text-sm font-semibold text-gray-700 bg-white/80 backdrop-blur-sm hover:bg-white hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200" do %>
              <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Export
            <% end %>
            <%= link_to import_contacts_path, 
                class: "inline-flex items-center px-5 py-3 border border-gray-300 rounded-xl shadow-sm text-sm font-semibold text-gray-700 bg-white/80 backdrop-blur-sm hover:bg-white hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200" do %>
              <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
              </svg>
              Import
            <% end %>
            <%= link_to new_contact_path, 
                class: "inline-flex items-center px-6 py-3 border border-transparent rounded-xl shadow-lg text-sm font-semibold text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 transform hover:scale-105" do %>
              <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Add Contact
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

    <!-- Enhanced Search and Filters -->
    <div class="bg-white shadow-lg rounded-xl border border-gray-100 hover:shadow-xl transition-shadow duration-200 mb-8"
         data-controller="search contact-management"
         data-search-url-value="<%= contacts_path %>"
         data-search-debounce-value="300"
         data-search-min-length-value="2">
      <div class="px-6 py-6">
        <!-- Search Header -->
        <div class="flex items-center justify-between mb-6">
          <div>
            <h3 class="text-lg font-semibold text-gray-900">Search & Filter Contacts</h3>
            <p class="text-sm text-gray-600 mt-1">Find contacts quickly with real-time search and advanced filters</p>
          </div>
          <div class="flex items-center space-x-2">
            <span class="text-sm text-gray-500">Total: <%= @contacts.respond_to?(:total_count) ? @contacts.total_count : @contacts.count %></span>
          </div>
        </div>

        <%= form_with url: contacts_path, method: :get, local: true, class: "space-y-6" do |form| %>
          <!-- Real-time Search -->
          <div class="relative">
            <%= form.label :search, "Search contacts", class: "block text-sm font-medium text-gray-700 mb-2" %>
            <div class="relative">
              <%= form.text_field :search,
                  value: params[:search],
                  placeholder: "Search by name, email, or tags...",
                  class: "block w-full pl-10 pr-10 py-3 border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm transition-all duration-200",
                  data: {
                    search_target: "input",
                    action: "input->search#input keydown->search#keydown"
                  } %>

              <!-- Search Icon -->
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>

              <!-- Clear Button -->
              <div data-search-target="clearButton" class="absolute inset-y-0 right-0 pr-3 flex items-center" style="display: none;">
                <button type="button" class="text-gray-400 hover:text-gray-600 focus:outline-none transition-colors duration-200" data-action="click->search#clear">
                  <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <!-- Loading Indicator -->
              <div data-search-target="loading" class="absolute inset-y-0 right-0 pr-3 flex items-center" style="display: none;">
                <svg class="animate-spin h-5 w-5 text-indigo-500" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </div>
            </div>

            <!-- Enhanced Search Results Dropdown -->
            <div class="hidden absolute z-20 mt-2 w-full bg-white/95 backdrop-blur-sm shadow-2xl max-h-80 rounded-2xl py-2 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm border border-gray-200/50" data-search-target="results">
              <!-- Results will be populated here -->
            </div>
          </div>
          
          <!-- Enhanced Filters Grid -->
          <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-5">
            <div class="space-y-2">
              <%= form.label :status, "Status", class: "block text-sm font-semibold text-gray-700" %>
              <%= form.select :status, 
                  options_for_select([
                    ['All Statuses', ''],
                    ['✅ Subscribed', 'subscribed'],
                    ['❌ Unsubscribed', 'unsubscribed']
                  ], params[:status]),
                  {},
                  { class: "py-3 px-4 block w-full border-2 border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm bg-white/80 backdrop-blur-sm transition-all duration-200 hover:shadow-md" } %>
            </div>
            
            <div class="space-y-2">
              <%= form.label :tag, "Tag", class: "block text-sm font-semibold text-gray-700" %>
              <%= form.collection_select :tag, 
                  @current_account.tags.order(:name), 
                  :id, :name, 
                  { prompt: "🏷️ All Tags" },
                  { class: "py-3 px-4 block w-full border-2 border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm bg-white/80 backdrop-blur-sm transition-all duration-200 hover:shadow-md" } %>
            </div>
            
            <div class="space-y-2">
              <%= form.label :sort, "Sort By", class: "block text-sm font-semibold text-gray-700" %>
              <%= form.select :sort, 
                  options_for_select([
                    ['📅 Recently Added', 'created_at_desc'],
                    ['📅 Oldest First', 'created_at_asc'],
                    ['🔤 Name A-Z', 'name_asc'],
                    ['🔤 Name Z-A', 'name_desc'],
                    ['📧 Email A-Z', 'email_asc'],
                    ['📧 Email Z-A', 'email_desc']
                  ], params[:sort] || 'created_at_desc'),
                  {},
                  { class: "py-3 px-4 block w-full border-2 border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm bg-white/80 backdrop-blur-sm transition-all duration-200 hover:shadow-md" } %>
            </div>
            
            <div class="flex items-end space-x-3">
              <%= form.submit "Apply Filters", 
                  class: "inline-flex items-center px-6 py-3 border border-transparent text-sm font-semibold rounded-xl shadow-lg text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 transform hover:scale-105" %>
            </div>
            
            <% if params[:search].present? || params[:status].present? || params[:tag].present? || params[:sort].present? %>
              <div class="flex items-end">
                <%= link_to "Clear All", contacts_path, 
                    class: "inline-flex items-center px-6 py-3 border-2 border-gray-300 text-sm font-semibold rounded-xl shadow-sm text-gray-700 bg-white/80 backdrop-blur-sm hover:bg-white hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200" do %>
                  <svg class="-ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                  Clear
                <% end %>
              </div>
            <% end %>
          </div>
        <% end %>
    </div>
  </div>

    <!-- Enhanced Bulk Actions -->
    <% if @contacts.any? %>
      <div class="bg-white/80 backdrop-blur-sm shadow-xl rounded-2xl border border-gray-200/50 p-6 mb-8">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-6">
            <label class="flex items-center group cursor-pointer">
              <input type="checkbox" id="select-all" 
                     class="rounded-lg border-2 border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-2 focus:ring-indigo-500 focus:ring-opacity-50 transition-all duration-200 w-5 h-5">
              <span class="ml-3 text-sm font-semibold text-gray-700 group-hover:text-gray-900 transition-colors">Select All</span>
            </label>
            
            <div class="hidden" id="bulk-actions">
              <div class="flex items-center space-x-3">
                <button type="button" id="bulk-tag" disabled
                        class="inline-flex items-center px-4 py-2 border-2 border-gray-300 shadow-sm text-sm font-semibold rounded-xl text-gray-700 bg-white/80 backdrop-blur-sm hover:bg-white hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 transition-all duration-200">
                  <svg class="-ml-0.5 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                  </svg>
                  Add Tag
                </button>
                
                <button type="button" id="bulk-unsubscribe" disabled
                        class="inline-flex items-center px-4 py-2 border-2 border-orange-300 shadow-sm text-sm font-semibold rounded-xl text-orange-700 bg-white/80 backdrop-blur-sm hover:bg-orange-50 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 transition-all duration-200">
                  <svg class="-ml-0.5 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728" />
                  </svg>
                  Unsubscribe
                </button>
                
                <button type="button" id="bulk-delete" disabled
                        class="inline-flex items-center px-4 py-2 border-2 border-red-300 shadow-sm text-sm font-semibold rounded-xl text-red-700 bg-white/80 backdrop-blur-sm hover:bg-red-50 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 transition-all duration-200">
                  <svg class="-ml-0.5 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                  Delete
                </button>
                
                <%= link_to export_contacts_path(format: :csv, search: params[:search], status: params[:status], tag: params[:tag]), 
                    class: "inline-flex items-center px-4 py-2 border-2 border-blue-300 shadow-sm text-sm font-semibold rounded-xl text-blue-700 bg-white/80 backdrop-blur-sm hover:bg-blue-50 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200" do %>
                  <svg class="-ml-0.5 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  Export
                <% end %>
              </div>
            </div>
          </div>
          
          <div class="flex items-center space-x-4">
            <div class="text-sm text-gray-500">
              <span class="font-semibold text-gray-700"><%= @contacts.respond_to?(:total_count) ? @contacts.total_count : @contacts.count %></span> contacts found
            </div>
            <div class="flex items-center space-x-2">
              <button type="button" class="p-2 text-gray-400 hover:text-gray-600 transition-colors" title="Grid View">
                <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                </svg>
              </button>
              <button type="button" class="p-2 text-indigo-600 hover:text-indigo-700 transition-colors" title="List View">
                <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    <% end %>

    <!-- Enhanced Contacts Table -->
    <div class="bg-white shadow-lg rounded-xl border border-gray-100 overflow-hidden">
    <% if @contacts.any? %>
      <div class="bg-white/80 backdrop-blur-sm shadow-xl rounded-2xl border border-gray-200/50 overflow-hidden">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gradient-to-r from-gray-50 to-gray-100">
              <tr>
                <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">
                  <input type="checkbox" id="select-all-table" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                </th>
                <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">
                  Contact
                </th>
                <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">
                  Tags
                </th>
                <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">
                  Email Stats
                </th>
                <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">
                  Engagement
                </th>
                <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">
                  Last Activity
                </th>
                <th scope="col" class="relative px-6 py-4">
                  <span class="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <% @contacts.each_with_index do |contact, index| %>
                <tr class="hover:bg-gray-50 transition-colors duration-200 contact-row <%= 'bg-gray-50/50' if index.even? %>" data-contact-id="<%= contact.id %>">
                  <!-- Checkbox -->
                  <td class="px-6 py-4 whitespace-nowrap">
                    <input type="checkbox" class="contact-checkbox rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" value="<%= contact.id %>">
                  </td>
                  
                  <!-- Contact Info with Avatar -->
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center space-x-4">
                      <div class="flex-shrink-0">
                        <div class="h-12 w-12 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center text-white font-bold text-lg shadow-lg cursor-pointer hover:shadow-xl transition-shadow duration-200" 
                             onclick="window.location.href='<%= contact_path(contact) %>'">
                          <%= contact.full_name.first.upcase %>
                        </div>
                      </div>
                      <div class="min-w-0 flex-1">
                        <div class="text-sm font-bold text-gray-900">
                          <%= link_to contact.full_name, contact_path(contact), 
                              class: "hover:text-indigo-600 transition-colors duration-200" %>
                        </div>
                        <div class="text-sm text-gray-500 truncate">
                          <%= link_to contact.email, "mailto:#{contact.email}", 
                              class: "hover:text-indigo-600 transition-colors duration-200" %>
                        </div>
                        <div class="text-xs text-gray-400">
                          Added <%= time_ago_in_words(contact.created_at) %> ago
                        </div>
                      </div>
                    </div>
                  </td>
                  
                  <!-- Status -->
                  <td class="px-6 py-4 whitespace-nowrap">
                    <% if contact.status == 'subscribed' %>
                      <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-green-100 text-green-800 border border-green-200">
                        <svg class="-ml-0.5 mr-1.5 h-3 w-3 text-green-500" fill="currentColor" viewBox="0 0 8 8">
                          <circle cx="4" cy="4" r="3" />
                        </svg>
                        Active
                      </span>
                    <% else %>
                      <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-red-100 text-red-800 border border-red-200">
                        <svg class="-ml-0.5 mr-1.5 h-3 w-3 text-red-500" fill="currentColor" viewBox="0 0 8 8">
                          <circle cx="4" cy="4" r="3" />
                        </svg>
                        Inactive
                      </span>
                    <% end %>
                  </td>
                  
                  <!-- Tags -->
                  <td class="px-6 py-4">
                    <% if contact.tags.any? %>
                      <div class="flex flex-wrap gap-1">
                        <% contact.tags.limit(2).each do |tag| %>
                          <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-indigo-100 text-indigo-800 border border-indigo-200">
                            <%= tag.name %>
                          </span>
                        <% end %>
                        <% if contact.tags.count > 2 %>
                          <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800 border border-gray-200" 
                                title="<%= contact.tags.offset(2).pluck(:name).join(', ') %>">
                            +<%= contact.tags.count - 2 %>
                          </span>
                        <% end %>
                      </div>
                    <% else %>
                      <span class="text-sm text-gray-400">No tags</span>
                    <% end %>
                  </td>
                  
                  <!-- Email Stats -->
                  <td class="px-6 py-4 whitespace-nowrap">
                    <% emails_received = contact.campaign_contacts.where.not(sent_at: nil).count %>
                    <% emails_opened = contact.campaign_contacts.where.not(opened_at: nil).count %>
                    <div class="flex items-center space-x-4">
                      <div class="text-center">
                        <div class="text-sm font-bold text-blue-600"><%= emails_received %></div>
                        <div class="text-xs text-gray-500">Sent</div>
                      </div>
                      <div class="text-center">
                        <div class="text-sm font-bold text-green-600"><%= emails_opened %></div>
                        <div class="text-xs text-gray-500">Opened</div>
                      </div>
                    </div>
                  </td>
                  
                  <!-- Engagement Rate -->
                  <td class="px-6 py-4 whitespace-nowrap">
                    <% if emails_received > 0 %>
                      <% engagement_rate = (emails_opened.to_f / emails_received * 100).round(1) %>
                      <div class="flex items-center space-x-2">
                        <div class="flex-1">
                          <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-gradient-to-r from-green-400 to-green-600 h-2 rounded-full transition-all duration-300" 
                                 style="width: <%= engagement_rate %>%"></div>
                          </div>
                        </div>
                        <span class="text-sm font-medium text-gray-900"><%= engagement_rate %>%</span>
                      </div>
                    <% else %>
                      <span class="text-sm text-gray-400">No data</span>
                    <% end %>
                  </td>
                  
                  <!-- Last Activity -->
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <% if contact.last_opened_at %>
                      <div class="flex items-center">
                        <svg class="flex-shrink-0 mr-1.5 h-4 w-4 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        <span><%= time_ago_in_words(contact.last_opened_at) %> ago</span>
                      </div>
                    <% else %>
                      <span class="text-gray-400">Never</span>
                    <% end %>
                  </td>
                  
                  <!-- Actions -->
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div class="flex items-center justify-end space-x-2">
                      <%= link_to contact_path(contact), 
                          class: "p-2 text-gray-400 hover:text-indigo-600 hover:bg-indigo-50 rounded-lg transition-all duration-200",
                          title: "View Contact" do %>
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                      <% end %>
                      
                      <%= link_to edit_contact_path(contact), 
                          class: "p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200",
                          title: "Edit Contact" do %>
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                      <% end %>
                      
                      <button type="button" 
                              class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-50 rounded-lg transition-all duration-200"
                              title="More Options"
                              data-contact-id="<%= contact.id %>"
                              data-contact-name="<%= contact.full_name %>"
                              data-contact-email="<%= contact.email %>"
                              data-contact-status="<%= contact.status %>"
                              data-contact-tags='<%= contact.tags.pluck(:name).to_json %>'
                              data-action="click->contact-modal#open">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                        </svg>
                      </button>
                    </div>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      </div>
      
      <!-- Enhanced Pagination -->
      <% if @contacts.respond_to?(:total_pages) %>
        <div class="bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 border-t border-gray-200/50">
          <div class="flex flex-col sm:flex-row items-center justify-between space-y-3 sm:space-y-0">
            <div class="flex items-center space-x-2">
              <div class="flex items-center px-3 py-2 bg-white rounded-lg shadow-sm border border-gray-200">
                <svg class="h-4 w-4 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                <span class="text-sm font-medium text-gray-700">
                  Showing <span class="text-indigo-600"><%= (@contacts.current_page - 1) * @contacts.limit_value + 1 %></span> to 
                  <span class="text-indigo-600"><%= [@contacts.current_page * @contacts.limit_value, @contacts.total_count].min %></span> of 
                  <span class="text-indigo-600"><%= @contacts.total_count %></span> contacts
                </span>
              </div>
            </div>
            
            <div class="flex items-center space-x-2">
              <%= paginate @contacts if defined?(Kaminari) %>
            </div>
          </div>
        </div>
      <% end %>
    <% else %>
      <!-- Enhanced Empty State -->
      <div class="text-center py-16 px-6">
        <div class="max-w-md mx-auto">
          <div class="mx-auto h-24 w-24 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-full flex items-center justify-center mb-6">
            <svg class="h-12 w-12 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
          </div>
          
          <h3 class="text-2xl font-bold text-gray-900 mb-2">
            <% if params[:search].present? || params[:status].present? || params[:tag].present? %>
              No contacts found
            <% else %>
              No contacts yet
            <% end %>
          </h3>
          <p class="text-gray-600 mb-8 leading-relaxed">
            <% if params[:search].present? || params[:status].present? || params[:tag].present? %>
              Try adjusting your search or filter criteria to find what you're looking for.
            <% else %>
              Start building your contact list by adding individual contacts or importing from your existing database. 
              Your contacts will appear here once added.
            <% end %>
          </p>
          
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <% if params[:search].present? || params[:status].present? || params[:tag].present? %>
              <%= link_to "Clear filters", contacts_path, 
                  class: "inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-xl text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-0.5" %>
            <% else %>
              <%= link_to new_contact_path, 
                  class: "inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-xl text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-0.5" do %>
                <svg class="-ml-1 mr-3 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Add Your First Contact
              <% end %>
              
              <%= link_to "#", 
                  class: "inline-flex items-center justify-center px-6 py-3 border-2 border-gray-300 text-base font-medium rounded-xl text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 shadow-sm hover:shadow-md transition-all duration-200" do %>
                <svg class="-ml-1 mr-3 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                </svg>
                Import Contacts
              <% end %>
            <% end %>
          </div>
          
          <% unless params[:search].present? || params[:status].present? || params[:tag].present? %>
            <div class="mt-8 p-4 bg-blue-50 rounded-xl border border-blue-200">
              <div class="flex items-start space-x-3">
                <svg class="flex-shrink-0 h-5 w-5 text-blue-500 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div class="text-left">
                  <h4 class="text-sm font-semibold text-blue-900">Pro Tip</h4>
                  <p class="text-sm text-blue-700 mt-1">
                    You can import contacts from CSV files, or add them one by one. 
                    Each contact can be tagged and tracked for email engagement.
                  </p>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    <% end %>
    </div>
  </div>
</div>

  <!-- Contact Actions Modal -->
  <div data-controller="contact-modal" data-contact-modal-target="modal" class="hidden fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div data-contact-modal-target="backdrop" 
         data-action="click->contact-modal#backdropClick"
         class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0 bg-gray-500 bg-opacity-75 transition-opacity opacity-0">
      
      <!-- This element is to trick the browser into centering the modal contents. -->
      <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
      
      <div data-contact-modal-target="content"
           class="inline-block align-bottom bg-white rounded-2xl px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6 opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
        
        <!-- Modal Header -->
        <div class="sm:flex sm:items-start">
          <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-gradient-to-r from-indigo-500 to-purple-600 sm:mx-0 sm:h-10 sm:w-10">
            <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>
          <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left flex-1">
            <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
              Contact Actions
            </h3>
            <div class="mt-2">
              <div class="space-y-2">
                <div class="flex items-center space-x-2">
                  <h4 data-contact-modal-target="contactName" class="text-sm font-semibold text-gray-900"></h4>
                  <span data-contact-modal-target="contactStatus" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"></span>
                </div>
                <p data-contact-modal-target="contactEmail" class="text-sm text-gray-600"></p>
                <div class="flex flex-wrap gap-1" data-contact-modal-target="contactTags"></div>
              </div>
            </div>
          </div>
          <div class="absolute top-0 right-0 pt-4 pr-4">
            <button type="button" 
                    data-action="click->contact-modal#close"
                    class="bg-white rounded-md text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500">
              <span class="sr-only">Close</span>
              <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
        
        <!-- Modal Actions -->
        <div class="mt-6 grid grid-cols-1 gap-3 sm:grid-cols-2">
          <!-- View Action -->
           <button type="button" 
                    data-action="click->contact-modal#handleView"
                    class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
              <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
              View Details
            </button>
          
          <!-- Edit Action -->
           <button type="button" 
                   data-action="click->contact-modal#handleEdit"
                   class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
             <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
               <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
             </svg>
             Edit Contact
           </button>
          
          <!-- Subscribe/Unsubscribe Action -->
          <button type="button" 
                  data-action="click->contact-modal#handleSubscriptionToggle"
                  data-action="subscribe"
                  class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200">
            <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <!-- Icon will be updated by JavaScript -->
            </svg>
            <!-- Text will be updated by JavaScript -->
          </button>
          
          <!-- Delete Action -->
          <button type="button" 
                  data-action="click->contact-modal#handleDelete"
                  data-action="delete"
                  class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200">
            <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
            Delete Contact
          </button>
        </div>
        
        <!-- Modal Footer -->
        <div class="mt-6 flex justify-end">
          <button type="button" 
                  data-action="click->contact-modal#close"
                  class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('select-all-table');
    const contactCheckboxes = document.querySelectorAll('.contact-checkbox');
    const bulkButtons = document.querySelectorAll('#bulk-tag, #bulk-unsubscribe, #bulk-delete');
    
    function updateBulkButtons() {
      const checkedBoxes = document.querySelectorAll('.contact-checkbox:checked');
      const hasSelection = checkedBoxes.length > 0;
      
      bulkButtons.forEach(button => {
        button.disabled = !hasSelection;
      });
    }
    
    selectAllCheckbox?.addEventListener('change', function() {
      contactCheckboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
      });
      updateBulkButtons();
    });
    
    contactCheckboxes.forEach(checkbox => {
      checkbox.addEventListener('change', function() {
        const allChecked = Array.from(contactCheckboxes).every(cb => cb.checked);
        const noneChecked = Array.from(contactCheckboxes).every(cb => !cb.checked);
        
        if (selectAllCheckbox) {
          selectAllCheckbox.checked = allChecked;
          selectAllCheckbox.indeterminate = !allChecked && !noneChecked;
        }
        
        updateBulkButtons();
      });
    });
    
    // Initialize bulk button states
    updateBulkButtons();
  });
</script>