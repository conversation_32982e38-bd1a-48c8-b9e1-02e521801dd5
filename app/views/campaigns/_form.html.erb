<%= form_with(model: campaign, local: true, class: "space-y-8", data: { controller: "campaign-form" }) do |form| %>
  <% if campaign.errors.any? %>
    <div class="rounded-lg bg-red-50 p-4 border border-red-200">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">
            There <%= campaign.errors.count == 1 ? 'was' : 'were' %> <%= pluralize(campaign.errors.count, "error") %> with your submission:
          </h3>
          <div class="mt-2 text-sm text-red-700">
            <ul role="list" class="list-disc pl-5 space-y-1">
              <% campaign.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <!-- Campaign Header -->
  <div class="bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-600 rounded-2xl p-8 text-white shadow-xl border-0 overflow-hidden relative">
    <div class="absolute inset-0 bg-black bg-opacity-10"></div>
    <div class="relative z-10">
      <div class="flex items-center mb-4">
        <div class="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex items-center justify-center mr-4">
          <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
        </div>
        <div>
          <h2 class="text-3xl font-bold mb-1">
            <%= campaign.persisted? ? "Edit Campaign" : "Create New Campaign" %>
          </h2>
          <p class="text-white text-opacity-90 text-lg">
            Design beautiful campaigns for email, Instagram, X (Twitter), and Facebook
          </p>
        </div>
      </div>
      <div class="mt-6 flex items-center space-x-4 text-sm text-white text-opacity-80">
        <div class="flex items-center">
          <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          Multi-platform support
        </div>
        <div class="flex items-center">
          <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
          </svg>
          Smart targeting
        </div>
        <div class="flex items-center">
          <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
          </svg>
          Rich media
        </div>
      </div>
    </div>
  </div>

  <!-- Campaign Type Selection -->
  <div class="bg-white rounded-2xl shadow-lg border-0 p-8 hover:shadow-xl transition-shadow duration-300" style="box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(0, 0, 0, 0.05);">
    <div class="flex items-center mb-6">
      <div class="bg-gradient-to-br from-indigo-100 to-purple-100 rounded-xl p-3 mr-4 shadow-sm">
        <svg class="w-7 h-7 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2"></path>
        </svg>
      </div>
      <div>
        <h3 class="text-xl font-bold text-gray-900">Campaign Type & Platforms</h3>
        <p class="text-sm text-gray-600 mt-1">Choose your media type and target platforms</p>
      </div>
    </div>
    
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-3">Media Type</label>
        <div class="grid grid-cols-2 gap-4">
          <% %w[text image video audio mixed].each do |type| %>
            <label class="group relative flex items-center p-4 border-2 border-gray-200 rounded-xl cursor-pointer hover:border-indigo-300 hover:bg-indigo-50 transition-all duration-200 hover:shadow-md">
              <%= form.radio_button :media_type, type, class: "sr-only peer" %>
              <div class="flex items-center space-x-3 w-full">
                <div class="w-5 h-5 border-2 border-gray-300 rounded-full peer-checked:border-indigo-600 peer-checked:bg-indigo-600 relative transition-all duration-200 group-hover:border-indigo-400">
                  <div class="absolute inset-1 bg-white rounded-full peer-checked:bg-indigo-600 transition-all duration-200"></div>
                </div>
                <div class="flex items-center space-x-2">
                  <% case type %>
                  <% when 'text' %>
                    <svg class="w-4 h-4 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"></path>
                    </svg>
                  <% when 'image' %>
                    <svg class="w-4 h-4 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                    </svg>
                  <% when 'video' %>
                    <svg class="w-4 h-4 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"></path>
                    </svg>
                  <% when 'audio' %>
                    <svg class="w-4 h-4 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM15.657 6.343a1 1 0 011.414 0A9.972 9.972 0 0119 12a9.972 9.972 0 01-1.929 5.657 1 1 0 11-1.414-1.414A7.971 7.971 0 0017 12c0-2.21-.895-4.21-2.343-5.657a1 1 0 010-1.414zm-2.829 2.828a1 1 0 011.415 0A5.983 5.983 0 0115 12a5.983 5.983 0 01-.757 2.829 1 1 0 11-1.415-1.414A3.987 3.987 0 0013 12a3.987 3.987 0 00-.172-1.415 1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                  <% when 'mixed' %>
                    <svg class="w-4 h-4 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                    </svg>
                  <% end %>
                  <span class="text-sm font-medium text-gray-700 capitalize"><%= type %></span>
                </div>
              </div>
            </label>
          <% end %>
        </div>
      </div>
      
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-3">Target Platforms</label>
        <div class="space-y-3">
          <% [['email', 'Email', '#3b82f6'], ['instagram', 'Instagram', '#e1306c'], ['twitter', 'X (Twitter)', '#000000'], ['facebook', 'Facebook', '#1877f2']].each do |platform, name, color| %>
            <label class="group flex items-center p-4 border-2 border-gray-200 rounded-xl hover:border-indigo-300 hover:bg-indigo-50 cursor-pointer transition-all duration-200 hover:shadow-md">
              <%= check_box_tag "campaign[social_platforms_array][]", platform,
                    campaign.social_platforms_array.include?(platform),
                    class: "rounded-lg border-gray-300 text-indigo-600 focus:ring-indigo-500 focus:ring-2 w-5 h-5 transition-all duration-200" %>
              <div class="ml-4 flex items-center space-x-3">
                <div class="w-6 h-6 rounded-lg shadow-sm flex items-center justify-center" style="background-color: <%= color %>">
                  <% case platform %>
                  <% when 'email' %>
                    <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                      <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                    </svg>
                  <% when 'instagram' %>
                    <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                    </svg>
                  <% when 'twitter' %>
                    <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                    </svg>
                  <% when 'facebook' %>
                    <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                    </svg>
                  <% end %>
                </div>
                <span class="text-sm font-semibold text-gray-700 group-hover:text-indigo-700 transition-colors duration-200"><%= name %></span>
              </div>
            </label>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Basic Information -->
  <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
    <div class="flex items-center mb-6">
      <div class="bg-green-100 rounded-lg p-2 mr-3">
        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
      </div>
      <h3 class="text-lg font-semibold text-gray-900">Campaign Information</h3>
    </div>
    
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div class="space-y-4">
        <div>
          <%= form.label :name, class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.text_field :name, 
              class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors",
              placeholder: "Enter campaign name" %>
        </div>
        
        <div>
          <%= form.label :subject, class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.text_field :subject, 
              class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors",
              placeholder: "Enter email subject line" %>
        </div>
      </div>
      
      <div class="space-y-4">
        <div>
          <%= form.label :from_name, class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.text_field :from_name, 
              class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors",
              placeholder: "Sender name" %>
        </div>
        
        <div>
          <%= form.label :from_email, class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.email_field :from_email, 
              class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors",
              placeholder: "<EMAIL>" %>
        </div>
      </div>
    </div>
  </div>

  <!-- Design & Branding -->
  <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
    <div class="flex items-center mb-6">
      <div class="bg-purple-100 rounded-lg p-2 mr-3">
        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
        </svg>
      </div>
      <h3 class="text-lg font-semibold text-gray-900">Design & Branding</h3>
    </div>
    
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-3">Design Theme</label>
        <div class="space-y-2">
          <% %w[modern classic elegant minimal bold].each do |theme| %>
            <label class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
              <%= form.radio_button :design_theme, theme, class: "text-indigo-600 focus:ring-indigo-500" %>
              <span class="ml-3 text-sm font-medium text-gray-700 capitalize"><%= theme %></span>
            </label>
          <% end %>
        </div>
      </div>
      
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-3">Font Family</label>
        <%= form.select :font_family, 
            options_for_select([
              ['Inter', 'Inter'],
              ['Roboto', 'Roboto'],
              ['Poppins', 'Poppins'],
              ['Montserrat', 'Montserrat'],
              ['Lato', 'Lato'],
              ['Open Sans', 'Open-Sans']
            ], campaign.font_family),
            {},
            { class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" } %>
      </div>
      
      <div class="space-y-4">
        <div>
          <%= form.label :background_color, "Background Color", class: "block text-sm font-medium text-gray-700 mb-2" %>
          <div class="flex space-x-2">
            <%= form.color_field :background_color, 
                class: "w-16 h-12 border border-gray-300 rounded-lg cursor-pointer" %>
            <%= form.text_field :background_color, 
                class: "flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500",
                placeholder: "#ffffff" %>
          </div>
        </div>
        
        <div>
          <%= form.label :text_color, "Text Color", class: "block text-sm font-medium text-gray-700 mb-2" %>
          <div class="flex space-x-2">
            <%= form.color_field :text_color, 
                class: "w-16 h-12 border border-gray-300 rounded-lg cursor-pointer" %>
            <%= form.text_field :text_color, 
                class: "flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500",
                placeholder: "#1f2937" %>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Media & Assets -->
  <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
    <div class="flex items-center mb-6">
      <div class="bg-pink-100 rounded-lg p-2 mr-3">
        <svg class="w-6 h-6 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
        </svg>
      </div>
      <h3 class="text-lg font-semibold text-gray-900">Media & Assets</h3>
    </div>
    
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div class="space-y-4">
        <div>
          <%= form.label :header_image_url, "Header Image URL", class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.url_field :header_image_url, 
              class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500",
              placeholder: "https://example.com/header-image.jpg" %>
        </div>
        
        <div>
          <%= form.label :logo_url, "Logo URL", class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.url_field :logo_url, 
              class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500",
              placeholder: "https://example.com/logo.png" %>
        </div>
      </div>
      
      <div class="space-y-4">
        <div>
          <%= form.label :call_to_action_text, "Call to Action Text", class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.text_field :call_to_action_text, 
              class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500",
              placeholder: "Shop Now, Learn More, etc." %>
        </div>
        
        <div>
          <%= form.label :call_to_action_url, "Call to Action URL", class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.url_field :call_to_action_url, 
              class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500",
              placeholder: "https://example.com/landing-page" %>
        </div>
      </div>
    </div>
    
    <div class="mt-6">
      <label class="block text-sm font-medium text-gray-700 mb-2">Media URLs (JSON format for multiple files)</label>
      <%= form.text_area :media_urls, 
          rows: 3,
          class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500",
          placeholder: '["https://example.com/video.mp4", "https://example.com/image.jpg"]' %>
      <p class="mt-2 text-sm text-gray-500">
        Enter URLs in JSON array format for images, videos, or audio files.
      </p>
    </div>
  </div>

  <!-- Content -->
  <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
    <div class="flex items-center mb-6">
      <div class="bg-yellow-100 rounded-lg p-2 mr-3">
        <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
        </svg>
      </div>
      <h3 class="text-lg font-semibold text-gray-900">Campaign Content</h3>
    </div>
    
    <div class="space-y-6">
      <div>
        <%= form.label :template_id, "Template (optional)", class: "block text-sm font-medium text-gray-700 mb-2" %>
        <%= form.collection_select :template_id, 
            @current_account.templates.order(:name), 
            :id, :name, 
            { prompt: "Select a template or create custom content" },
            { class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" } %>
      </div>

      <div>
        <%= form.label :content, "Campaign Content", class: "block text-sm font-medium text-gray-700 mb-2" %>
        <%= form.text_area :content, 
            rows: 12,
            class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 font-mono text-sm",
            placeholder: "Enter your campaign content here. You can use HTML for email formatting.\n\nFor social media, use platform-specific formatting:\n- Instagram: Use hashtags and mentions\n- Twitter: Keep under 280 characters\n- Facebook: Engaging copy with call-to-action\n\nVariables like {{first_name}} and {{email}} will be replaced with contact information." %>
        <div class="mt-3 flex flex-wrap gap-2">
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            {{first_name}}
          </span>
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            {{last_name}}
          </span>
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            {{email}}
          </span>
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            {{company}}
          </span>
        </div>
      </div>
    </div>
  </div>

  <!-- Recipients & Scheduling -->
  <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
    <div class="flex items-center mb-6">
      <div class="bg-blue-100 rounded-lg p-2 mr-3">
        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
        </svg>
      </div>
      <h3 class="text-lg font-semibold text-gray-900">Recipients & Scheduling</h3>
    </div>
    
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-4">Send to</label>
        <div class="space-y-3">
          <% [['all', 'All contacts', 'Send to everyone in your contact list'],
               ['subscribed', 'Subscribed contacts only', 'Send only to contacts who are subscribed'],
               ['tags', 'Contacts with specific tags', 'Target specific groups using tags']].each do |value, label, description| %>
            <label class="relative flex items-start p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
              <%= form.radio_button :recipient_type, value,
                  class: "mt-1 text-indigo-600 focus:ring-indigo-500",
                  data: { action: "change->campaign-form#recipientTypeChanged" } %>
              <div class="ml-3">
                <div class="text-sm font-medium text-gray-900"><%= label %></div>
                <div class="text-sm text-gray-500"><%= description %></div>
              </div>
            </label>
          <% end %>
        </div>

        <div id="tag-selection" class="mt-4 <%= 'hidden' unless campaign.recipient_type == 'tags' %>" data-campaign-form-target="tagSelection">
          <label class="block text-sm font-medium text-gray-700 mb-3">Select Tags</label>
          <div class="max-h-48 overflow-y-auto border border-gray-300 rounded-lg p-4 space-y-2">
            <% @current_account.tags.each do |tag| %>
              <label class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                <%= check_box_tag "campaign[tag_ids][]", tag.id, 
                    campaign.tag_ids.include?(tag.id), 
                    class: "rounded border-gray-300 text-indigo-600 focus:ring-indigo-500" %>
                <span class="ml-3 text-sm text-gray-700"><%= tag.name %></span>
                <span class="ml-auto text-xs text-gray-500">(<%= tag.contacts.count %> contacts)</span>
              </label>
            <% end %>
          </div>
        </div>
      </div>
      
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-4">Scheduling</label>
        <div class="space-y-3">
          <% [['now', 'Send immediately', 'Campaign will be sent right away'],
               ['scheduled', 'Schedule for later', 'Choose a specific date and time']].each do |value, label, description| %>
            <label class="relative flex items-start p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
              <%= form.radio_button :send_type, value,
                  class: "mt-1 text-indigo-600 focus:ring-indigo-500",
                  data: { action: "change->campaign-form#sendTypeChanged" } %>
              <div class="ml-3">
                <div class="text-sm font-medium text-gray-900"><%= label %></div>
                <div class="text-sm text-gray-500"><%= description %></div>
              </div>
            </label>
          <% end %>
        </div>

        <div id="schedule-datetime" class="mt-4 <%= 'hidden' unless campaign.send_type == 'scheduled' %>" data-campaign-form-target="scheduleDatetime">
          <%= form.label :scheduled_at, "Schedule Date & Time", class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.datetime_local_field :scheduled_at, 
              class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" %>
        </div>
        
        <div class="mt-4">
          <label class="flex items-center">
            <%= form.check_box :social_sharing_enabled, 
                class: "rounded border-gray-300 text-indigo-600 focus:ring-indigo-500" %>
            <span class="ml-2 text-sm text-gray-700">Enable social sharing buttons</span>
          </label>
        </div>
      </div>
    </div>
  </div>

  <!-- Action Buttons -->
  <div class="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-4 pt-6 border-t border-gray-200">
    <%= link_to "Cancel", campaigns_path, 
        class: "w-full sm:w-auto px-6 py-3 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors text-center" %>
    
    <%= form.submit "Save as Draft", 
        name: "commit", value: "Save as Draft",
        class: "w-full sm:w-auto px-6 py-3 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors" %>
    
    <% if campaign.persisted? && campaign.draft? %>
      <%= form.submit "Send Campaign", 
          name: "commit", value: "Send Campaign",
          class: "w-full sm:w-auto px-6 py-3 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all",
          data: { confirm: "Are you sure you want to send this campaign? This action cannot be undone." } %>
    <% else %>
      <%= form.submit campaign.persisted? ? "Update Campaign" : "Create Campaign", 
          class: "w-full sm:w-auto px-6 py-3 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all" %>
    <% end %>
  </div>
<% end %>

<!-- Campaign form functionality is now handled by the campaign-form Stimulus controller -->

<style>
  /* Custom styles for enhanced visual appeal */
  .peer:checked ~ * .peer-checked\:border-indigo-600 {
    border-color: #4f46e5;
  }
  
  .peer:checked ~ * .peer-checked\:bg-indigo-600 {
    background-color: #4f46e5;
  }
  
  /* Smooth transitions */
  input[type="radio"], input[type="checkbox"] {
    transition: all 0.2s ease-in-out;
  }
  
  /* Enhanced focus states */
  input:focus {
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
  }
  
  /* Gradient button hover effects */
  .bg-gradient-to-r:hover {
    transform: translateY(-1px);
    box-shadow: 0 10px 25px rgba(79, 70, 229, 0.2);
  }
</style>