<!-- Campaign Creation Wizard -->
<div class="bg-white shadow-2xl rounded-2xl border-0 overflow-hidden"
     data-controller="campaign-wizard"
     data-campaign-wizard-total-steps-value="4"
     data-campaign-wizard-current-step-value="1"
     data-campaign-wizard-auto-save-value="true">

  <!-- Progress Bar -->
  <div class="bg-gradient-to-r from-indigo-50 to-purple-50 px-8 py-6 border-b border-gray-100">
    <div class="flex items-center justify-between mb-4">
      <h2 class="text-xl font-semibold text-gray-900">Campaign Setup</h2>
      <div class="text-sm text-gray-500">
        <span data-campaign-wizard-target="currentStep">Step 1</span> of 4
      </div>
    </div>

    <!-- Progress Steps -->
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-8">
        <div class="flex flex-col items-center cursor-pointer" data-campaign-wizard-target="progressStep" data-action="click->campaign-wizard#goToStep" data-step="1">
          <div class="step-circle w-8 h-8 rounded-full flex items-center justify-center bg-gradient-to-r from-indigo-600 to-purple-600 text-white text-sm font-semibold transition-all duration-300 ring-4 ring-indigo-100">1</div>
          <div class="step-label text-sm font-medium text-indigo-600 mt-2">Details</div>
        </div>
        <div class="flex-1 h-1 bg-gray-200 rounded-full mx-4">
          <div class="h-1 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-full transition-all duration-500" style="width: 0%" data-campaign-wizard-target="progressBar"></div>
        </div>
        <div class="flex flex-col items-center cursor-pointer" data-campaign-wizard-target="progressStep" data-action="click->campaign-wizard#goToStep" data-step="2">
          <div class="step-circle w-8 h-8 rounded-full flex items-center justify-center bg-gray-200 text-gray-500 text-sm font-semibold transition-all duration-300">2</div>
          <div class="step-label text-sm font-medium text-gray-400 mt-2">Audience</div>
        </div>
        <div class="flex-1 h-1 bg-gray-200 rounded-full mx-4"></div>
        <div class="flex flex-col items-center cursor-pointer" data-campaign-wizard-target="progressStep" data-action="click->campaign-wizard#goToStep" data-step="3">
          <div class="step-circle w-8 h-8 rounded-full flex items-center justify-center bg-gray-200 text-gray-500 text-sm font-semibold transition-all duration-300">3</div>
          <div class="step-label text-sm font-medium text-gray-400 mt-2">Content</div>
        </div>
        <div class="flex-1 h-1 bg-gray-200 rounded-full mx-4"></div>
        <div class="flex flex-col items-center cursor-pointer" data-campaign-wizard-target="progressStep" data-action="click->campaign-wizard#goToStep" data-step="4">
          <div class="step-circle w-8 h-8 rounded-full flex items-center justify-center bg-gray-200 text-gray-500 text-sm font-semibold transition-all duration-300">4</div>
          <div class="step-label text-sm font-medium text-gray-400 mt-2">Schedule</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Error Messages -->
  <% if campaign.errors.any? %>
    <div class="bg-red-50 border-l-4 border-red-400 p-4 m-6 rounded-lg">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">
            There <%= campaign.errors.count == 1 ? 'was' : 'were' %> <%= pluralize(campaign.errors.count, "error") %> with your submission:
          </h3>
          <div class="mt-2 text-sm text-red-700">
            <ul role="list" class="list-disc pl-5 space-y-1">
              <% campaign.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <!-- Form -->
  <%= form_with model: campaign, local: true,
      class: "campaign-wizard-form",
      data: { campaign_wizard_target: "form" } do |form| %>

    <!-- Fallback content in case wizard doesn't load -->
    <noscript>
      <div class="p-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-6">Create Campaign</h2>
        <div class="space-y-6">
          <div>
            <%= form.label :name, "Campaign Name", class: "block text-sm font-semibold text-gray-700 mb-2" %>
            <%= form.text_field :name, class: "block w-full px-4 py-3 border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm" %>
          </div>
          <div>
            <%= form.label :subject, "Subject Line", class: "block text-sm font-semibold text-gray-700 mb-2" %>
            <%= form.text_field :subject, class: "block w-full px-4 py-3 border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm" %>
          </div>
          <div>
            <%= form.submit "Create Campaign", class: "w-full px-6 py-3 bg-indigo-600 text-white rounded-xl font-semibold hover:bg-indigo-700" %>
          </div>
        </div>
      </div>
    </noscript>

    <!-- Debug: Test visibility -->
    <div class="p-4 bg-yellow-100 border border-yellow-300 rounded-lg m-4">
      <p class="text-yellow-800">🔧 Debug: If you can see this, the form is loading. The wizard should appear below.</p>
    </div>

    <!-- Step 1: Campaign Details -->
    <div data-campaign-wizard-target="step" class="p-8 block" data-step="1">
      <div class="max-w-2xl mx-auto">
        <div class="text-center mb-8">
          <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-2xl flex items-center justify-center">
            <svg class="w-8 h-8 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-gray-900 mb-2">Campaign Details</h3>
          <p class="text-gray-600">Let's start with the basic information about your campaign</p>
        </div>

        <div class="space-y-6">
          <!-- Campaign Name -->
          <div>
            <%= form.label :name, "Campaign Name", class: "block text-sm font-semibold text-gray-700 mb-2" %>
            <%= form.text_field :name,
                placeholder: "e.g., Summer Sale Newsletter",
                required: true,
                maxlength: 100,
                class: "block w-full px-4 py-3 border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm transition-all duration-200",
                data: { action: "input->campaign-wizard#validateField" } %>
            <div class="mt-1 flex justify-between">
              <p class="text-xs text-gray-500">Choose a descriptive name for internal reference</p>
              <p class="text-xs text-gray-400"><span id="name-count">0</span>/100</p>
            </div>
          </div>

          <!-- Subject Line -->
          <div>
            <%= form.label :subject, "Subject Line", class: "block text-sm font-semibold text-gray-700 mb-2" %>
            <%= form.text_field :subject,
                placeholder: "🌞 Summer Sale - Up to 50% Off!",
                required: true,
                maxlength: 78,
                class: "block w-full px-4 py-3 border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm transition-all duration-200",
                data: { action: "input->campaign-wizard#validateField" } %>
            <div class="mt-1 flex justify-between">
              <p class="text-xs text-gray-500">Keep it under 78 characters for optimal display</p>
              <p class="text-xs text-gray-400"><span id="subject-count">0</span>/78</p>
            </div>
          </div>

          <!-- From Name -->
          <div>
            <%= form.label :from_name, "From Name", class: "block text-sm font-semibold text-gray-700 mb-2" %>
            <%= form.text_field :from_name,
                placeholder: "Your Company Name",
                required: true,
                class: "block w-full px-4 py-3 border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm transition-all duration-200" %>
            <p class="mt-1 text-xs text-gray-500">This will appear as the sender name</p>
          </div>

          <!-- From Email -->
          <div>
            <%= form.label :from_email, "From Email", class: "block text-sm font-semibold text-gray-700 mb-2" %>
            <%= form.email_field :from_email,
                placeholder: "<EMAIL>",
                required: true,
                class: "block w-full px-4 py-3 border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm transition-all duration-200" %>
            <p class="mt-1 text-xs text-gray-500">Must be a verified email address</p>
          </div>

          <!-- Preview Text -->
          <div>
            <%= form.label :preview_text, "Preview Text (Optional)", class: "block text-sm font-semibold text-gray-700 mb-2" %>
            <%= form.text_field :preview_text,
                placeholder: "This appears after the subject line in email clients...",
                maxlength: 140,
                class: "block w-full px-4 py-3 border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm transition-all duration-200" %>
            <div class="mt-1 flex justify-between">
              <p class="text-xs text-gray-500">Appears as preview text in email clients</p>
              <p class="text-xs text-gray-400"><span id="preview-count">0</span>/140</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Step 2: Target Audience -->
    <div data-campaign-wizard-target="step" class="p-8 hidden" data-step="2">
      <div class="max-w-2xl mx-auto">
        <div class="text-center mb-8">
          <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-purple-100 to-pink-100 rounded-2xl flex items-center justify-center">
            <svg class="w-8 h-8 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-gray-900 mb-2">Select Your Audience</h3>
          <p class="text-gray-600">Choose who will receive this campaign</p>
        </div>

        <div class="space-y-4">
          <!-- All Contacts -->
          <label class="relative flex items-start p-6 bg-gradient-to-br from-gray-50 to-white rounded-2xl border-2 border-gray-200 hover:border-indigo-300 hover:shadow-lg transition-all duration-200 cursor-pointer group">
            <%= form.radio_button :recipient_type, "all",
                class: "sr-only",
                name: "campaign[recipient_type]" %>
            <div class="flex items-center h-5">
              <div class="radio-custom w-5 h-5 border-2 border-gray-300 rounded-full group-hover:border-indigo-500 transition-colors duration-200 flex items-center justify-center">
                <div class="w-2 h-2 bg-indigo-600 rounded-full opacity-0 transition-opacity duration-200"></div>
              </div>
            </div>
            <div class="ml-4 flex-1">
              <div class="flex items-center justify-between">
                <h4 class="text-lg font-semibold text-gray-900 group-hover:text-indigo-700 transition-colors duration-200">All Contacts</h4>
                <span class="text-sm font-medium text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
                  <%= @total_contacts_count || 0 %> contacts
                </span>
              </div>
              <p class="text-gray-600 mt-1">Send to everyone in your contact list, including unsubscribed contacts</p>
            </div>
          </label>

          <!-- Subscribed Only -->
          <label class="relative flex items-start p-6 bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl border-2 border-green-200 hover:border-green-400 hover:shadow-lg transition-all duration-200 cursor-pointer group">
            <%= form.radio_button :recipient_type, "subscribed",
                class: "sr-only",
                name: "campaign[recipient_type]",
                checked: true %>
            <div class="flex items-center h-5">
              <div class="radio-custom w-5 h-5 border-2 border-green-300 rounded-full group-hover:border-green-500 transition-colors duration-200 flex items-center justify-center">
                <div class="w-2 h-2 bg-green-600 rounded-full opacity-100 transition-opacity duration-200"></div>
              </div>
            </div>
            <div class="ml-4 flex-1">
              <div class="flex items-center justify-between">
                <h4 class="text-lg font-semibold text-gray-900 group-hover:text-green-700 transition-colors duration-200">Subscribed Contacts Only</h4>
                <span class="text-sm font-medium text-green-700 bg-green-100 px-3 py-1 rounded-full">
                  <%= @subscribed_contacts_count || 0 %> contacts
                </span>
              </div>
              <p class="text-gray-600 mt-1">Send only to contacts who are currently subscribed (recommended)</p>
            </div>
          </label>

          <!-- Tagged Contacts -->
          <label class="relative flex items-start p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl border-2 border-blue-200 hover:border-blue-400 hover:shadow-lg transition-all duration-200 cursor-pointer group">
            <%= form.radio_button :recipient_type, "tags",
                class: "sr-only",
                name: "campaign[recipient_type]" %>
            <div class="flex items-center h-5">
              <div class="radio-custom w-5 h-5 border-2 border-blue-300 rounded-full group-hover:border-blue-500 transition-colors duration-200 flex items-center justify-center">
                <div class="w-2 h-2 bg-blue-600 rounded-full opacity-0 transition-opacity duration-200"></div>
              </div>
            </div>
            <div class="ml-4 flex-1">
              <div class="flex items-center justify-between">
                <h4 class="text-lg font-semibold text-gray-900 group-hover:text-blue-700 transition-colors duration-200">Contacts with Specific Tags</h4>
                <span class="text-sm font-medium text-blue-700 bg-blue-100 px-3 py-1 rounded-full">
                  Custom
                </span>
              </div>
              <p class="text-gray-600 mt-1">Send to contacts with specific tags for targeted campaigns</p>

              <!-- Tag Selection (shown when this option is selected) -->
              <div class="mt-4 tag-selection hidden">
                <div class="grid grid-cols-2 gap-3">
                  <% (@all_tags || []).each do |tag| %>
                    <%
                      # Find the contact count for this tag from our preloaded data
                      tag_with_count = @tags_with_counts&.find { |t| t.id == tag.id }
                      contact_count = tag_with_count&.contacts_count || 0
                    %>
                    <label class="flex items-center p-3 bg-white rounded-lg border border-gray-200 hover:border-blue-300 transition-colors duration-200">
                      <%= check_box_tag "campaign[tag_ids][]", tag.id,
                          campaign.tag_ids&.include?(tag.id),
                          class: "rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50" %>
                      <div class="ml-3 flex-1">
                        <div class="text-sm font-medium text-gray-900"><%= tag.name %></div>
                        <div class="text-xs text-gray-500"><%= pluralize(contact_count, 'contact') %></div>
                      </div>
                    </label>
                  <% end %>
                </div>
              </div>
            </div>
          </label>
        </div>
      </div>
    </div>
    <!-- Step 3: Content Creation -->
    <div data-campaign-wizard-target="step" class="hidden" data-step="3">
      <div class="h-screen flex">
        <!-- Email Builder Sidebar -->
        <div class="w-80 bg-gray-50 border-r border-gray-200 flex flex-col">
          <!-- Template Selection -->
          <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Choose Starting Point</h3>
            <div class="space-y-3">
              <label class="flex items-center p-4 bg-white rounded-xl border-2 border-gray-200 hover:border-indigo-300 cursor-pointer transition-all duration-200">
                <%= form.radio_button :template_choice, "blank",
                    class: "sr-only",
                    name: "campaign[template_choice]",
                    checked: true %>
                <div class="flex items-center h-5">
                  <div class="radio-custom w-5 h-5 border-2 border-gray-300 rounded-full transition-colors duration-200 flex items-center justify-center">
                    <div class="w-2 h-2 bg-indigo-600 rounded-full opacity-100 transition-opacity duration-200"></div>
                  </div>
                </div>
                <div class="ml-3">
                  <h4 class="text-sm font-semibold text-gray-900">Start from Scratch</h4>
                  <p class="text-xs text-gray-600">Build your email with drag-and-drop components</p>
                </div>
              </label>

              <label class="flex items-center p-4 bg-white rounded-xl border-2 border-gray-200 hover:border-indigo-300 cursor-pointer transition-all duration-200">
                <%= form.radio_button :template_choice, "template",
                    class: "sr-only",
                    name: "campaign[template_choice]" %>
                <div class="flex items-center h-5">
                  <div class="radio-custom w-5 h-5 border-2 border-gray-300 rounded-full transition-colors duration-200 flex items-center justify-center">
                    <div class="w-2 h-2 bg-indigo-600 rounded-full opacity-0 transition-opacity duration-200"></div>
                  </div>
                </div>
                <div class="ml-3">
                  <h4 class="text-sm font-semibold text-gray-900">Use Template</h4>
                  <p class="text-xs text-gray-600">Choose from pre-designed templates</p>
                </div>
              </label>
            </div>

            <!-- Template Selection (shown when template option is selected) -->
            <div class="template-selection hidden mt-4">
              <%= form.collection_select :template_id,
                  @templates || [],
                  :id, :name,
                  { prompt: "Select a template" },
                  { class: "w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm" } %>
            </div>
          </div>

          <!-- Component Palette -->
          <div class="flex-1 p-6" data-controller="email-builder" data-email-builder-target="palette">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Components</h3>
            <div class="space-y-3">
              <!-- Text Component -->
              <div class="component-item p-4 bg-white rounded-xl border-2 border-gray-200 hover:border-indigo-300 hover:shadow-md cursor-grab active:cursor-grabbing transition-all duration-200"
                   data-component-type="text">
                <div class="flex items-center">
                  <div class="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                    <svg class="w-4 h-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7" />
                    </svg>
                  </div>
                  <div>
                    <h4 class="text-sm font-semibold text-gray-900">Text Block</h4>
                    <p class="text-xs text-gray-600">Add paragraphs and headings</p>
                  </div>
                </div>
              </div>

              <!-- Image Component -->
              <div class="component-item p-4 bg-white rounded-xl border-2 border-gray-200 hover:border-indigo-300 hover:shadow-md cursor-grab active:cursor-grabbing transition-all duration-200"
                   data-component-type="image">
                <div class="flex items-center">
                  <div class="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                    <svg class="w-4 h-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div>
                    <h4 class="text-sm font-semibold text-gray-900">Image</h4>
                    <p class="text-xs text-gray-600">Add photos and graphics</p>
                  </div>
                </div>
              </div>

              <!-- Button Component -->
              <div class="component-item p-4 bg-white rounded-xl border-2 border-gray-200 hover:border-indigo-300 hover:shadow-md cursor-grab active:cursor-grabbing transition-all duration-200"
                   data-component-type="button">
                <div class="flex items-center">
                  <div class="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                    <svg class="w-4 h-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" />
                    </svg>
                  </div>
                  <div>
                    <h4 class="text-sm font-semibold text-gray-900">Button</h4>
                    <p class="text-xs text-gray-600">Call-to-action buttons</p>
                  </div>
                </div>
              </div>

              <!-- Divider Component -->
              <div class="component-item p-4 bg-white rounded-xl border-2 border-gray-200 hover:border-indigo-300 hover:shadow-md cursor-grab active:cursor-grabbing transition-all duration-200"
                   data-component-type="divider">
                <div class="flex items-center">
                  <div class="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                    <svg class="w-4 h-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4" />
                    </svg>
                  </div>
                  <div>
                    <h4 class="text-sm font-semibold text-gray-900">Divider</h4>
                    <p class="text-xs text-gray-600">Horizontal line separator</p>
                  </div>
                </div>
              </div>

              <!-- Social Component -->
              <div class="component-item p-4 bg-white rounded-xl border-2 border-gray-200 hover:border-indigo-300 hover:shadow-md cursor-grab active:cursor-grabbing transition-all duration-200"
                   data-component-type="social">
                <div class="flex items-center">
                  <div class="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                    <svg class="w-4 h-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
                    </svg>
                  </div>
                  <div>
                    <h4 class="text-sm font-semibold text-gray-900">Social Links</h4>
                    <p class="text-xs text-gray-600">Social media icons</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- Main Email Builder Area -->
        <div class="flex-1 flex flex-col">
          <!-- Toolbar -->
          <div class="bg-white border-b border-gray-200 px-6 py-4" data-email-builder-target="toolbar">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-4">
                <!-- Device Preview Toggle -->
                <div class="flex items-center bg-gray-100 rounded-lg p-1">
                  <button type="button"
                          class="px-3 py-2 text-sm font-medium rounded-md bg-indigo-600 text-white transition-all duration-200"
                          data-email-builder-target="deviceToggle"
                          data-device="desktop">
                    <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </button>
                  <button type="button"
                          class="px-3 py-2 text-sm font-medium rounded-md bg-gray-200 text-gray-700 transition-all duration-200"
                          data-email-builder-target="deviceToggle"
                          data-device="tablet">
                    <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a1 1 0 001-1V4a1 1 0 00-1-1H8a1 1 0 00-1 1v16a1 1 0 001 1z" />
                    </svg>
                  </button>
                  <button type="button"
                          class="px-3 py-2 text-sm font-medium rounded-md bg-gray-200 text-gray-700 transition-all duration-200"
                          data-email-builder-target="deviceToggle"
                          data-device="mobile">
                    <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                    </svg>
                  </button>
                </div>

                <!-- Undo/Redo -->
                <div class="flex items-center space-x-2">
                  <button type="button"
                          class="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                          data-email-builder-target="undoButton"
                          data-action="click->email-builder#undo"
                          disabled>
                    <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
                    </svg>
                  </button>
                  <button type="button"
                          class="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                          data-email-builder-target="redoButton"
                          data-action="click->email-builder#redo"
                          disabled>
                    <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 10h-10a8 8 0 00-8 8v2m18-10l-6-6m6 6l-6 6" />
                    </svg>
                  </button>
                </div>
              </div>

              <div class="flex items-center space-x-4">
                <!-- Auto-save indicator -->
                <div class="text-sm text-gray-500">
                  <span data-email-builder-target="saveButton">Auto-saving...</span>
                </div>

                <!-- Preview Button -->
                <button type="button"
                        class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200">
                  <svg class="-ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                  Preview
                </button>
              </div>
            </div>
          </div>

          <!-- Email Canvas -->
          <div class="flex-1 bg-gray-100 p-6 overflow-auto">
            <div class="max-w-2xl mx-auto">
              <div class="bg-white rounded-lg shadow-lg min-h-96 transition-all duration-300"
                   data-email-builder-target="canvas"
                   data-drop-zone="true">
                <!-- Email content will be built here -->
                <div class="p-8 text-center text-gray-500 border-2 border-dashed border-gray-300 rounded-lg">
                  <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  <h3 class="text-lg font-medium text-gray-900 mb-2">Start Building Your Email</h3>
                  <p class="text-sm text-gray-600">Drag components from the sidebar to begin creating your email</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Live Preview Panel -->
        <div class="w-80 bg-white border-l border-gray-200 flex flex-col">
          <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Live Preview</h3>
            <p class="text-sm text-gray-600">See how your email will look to recipients</p>
          </div>

          <div class="flex-1 p-6 bg-gray-50 overflow-auto">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 min-h-96"
                 data-email-builder-target="preview">
              <!-- Live preview content -->
              <div class="p-6 text-center text-gray-500">
                <svg class="mx-auto h-8 w-8 text-gray-400 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                <p class="text-sm">Preview will appear here</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Step 4: Schedule & Send -->
    <div data-campaign-wizard-target="step" class="p-8 hidden" data-step="4">
      <div class="max-w-2xl mx-auto">
        <div class="text-center mb-8">
          <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-green-100 to-emerald-100 rounded-2xl flex items-center justify-center">
            <svg class="w-8 h-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-gray-900 mb-2">Schedule Your Campaign</h3>
          <p class="text-gray-600">Choose when to send your campaign to your audience</p>
        </div>

        <div class="space-y-6">
          <!-- Send Options -->
          <div>
            <h4 class="text-lg font-semibold text-gray-900 mb-4">When would you like to send?</h4>
            <div class="space-y-4">
              <!-- Send Now -->
              <label class="relative flex items-start p-6 bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl border-2 border-green-200 hover:border-green-400 hover:shadow-lg transition-all duration-200 cursor-pointer group">
                <%= form.radio_button :send_type, "now",
                    class: "sr-only",
                    name: "campaign[send_type]",
                    checked: true %>
                <div class="flex items-center h-5">
                  <div class="radio-custom w-5 h-5 border-2 border-green-300 rounded-full group-hover:border-green-500 transition-colors duration-200 flex items-center justify-center">
                    <div class="w-2 h-2 bg-green-600 rounded-full opacity-100 transition-opacity duration-200"></div>
                  </div>
                </div>
                <div class="ml-4 flex-1">
                  <div class="flex items-center justify-between">
                    <h4 class="text-lg font-semibold text-gray-900 group-hover:text-green-700 transition-colors duration-200">Send Immediately</h4>
                    <span class="text-sm font-medium text-green-700 bg-green-100 px-3 py-1 rounded-full">
                      Recommended
                    </span>
                  </div>
                  <p class="text-gray-600 mt-1">Campaign will be sent right after creation</p>
                </div>
              </label>

              <!-- Schedule for Later -->
              <label class="relative flex items-start p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl border-2 border-blue-200 hover:border-blue-400 hover:shadow-lg transition-all duration-200 cursor-pointer group">
                <%= form.radio_button :send_type, "scheduled",
                    class: "sr-only",
                    name: "campaign[send_type]" %>
                <div class="flex items-center h-5">
                  <div class="radio-custom w-5 h-5 border-2 border-blue-300 rounded-full group-hover:border-blue-500 transition-colors duration-200 flex items-center justify-center">
                    <div class="w-2 h-2 bg-blue-600 rounded-full opacity-0 transition-opacity duration-200"></div>
                  </div>
                </div>
                <div class="ml-4 flex-1">
                  <div class="flex items-center justify-between">
                    <h4 class="text-lg font-semibold text-gray-900 group-hover:text-blue-700 transition-colors duration-200">Schedule for Later</h4>
                    <span class="text-sm font-medium text-blue-700 bg-blue-100 px-3 py-1 rounded-full">
                      Advanced
                    </span>
                  </div>
                  <p class="text-gray-600 mt-1">Choose a specific date and time to send</p>

                  <!-- Schedule DateTime (shown when this option is selected) -->
                  <div class="mt-4 schedule-datetime hidden">
                    <div class="grid grid-cols-2 gap-4">
                      <div>
                        <%= form.label :scheduled_date, "Date", class: "block text-sm font-medium text-gray-700 mb-2" %>
                        <%= form.date_field :scheduled_date,
                            class: "block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm" %>
                      </div>
                      <div>
                        <%= form.label :scheduled_time, "Time", class: "block text-sm font-medium text-gray-700 mb-2" %>
                        <%= form.time_field :scheduled_time,
                            class: "block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm" %>
                      </div>
                    </div>
                    <div class="mt-3">
                      <%= form.label :timezone, "Timezone", class: "block text-sm font-medium text-gray-700 mb-2" %>
                      <%= form.select :timezone,
                          options_for_select([
                            ['Eastern Time (ET)', 'America/New_York'],
                            ['Central Time (CT)', 'America/Chicago'],
                            ['Mountain Time (MT)', 'America/Denver'],
                            ['Pacific Time (PT)', 'America/Los_Angeles'],
                            ['UTC', 'UTC']
                          ], 'America/New_York'),
                          {},
                          { class: "block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm" } %>
                    </div>
                  </div>
                </div>
              </label>
            </div>
          </div>

          <!-- Advanced Options -->
          <div class="bg-gray-50 rounded-xl p-6">
            <h4 class="text-lg font-semibold text-gray-900 mb-4">Advanced Options</h4>
            <div class="space-y-4">
              <!-- Email Tracking -->
              <div>
                <h5 class="text-sm font-semibold text-gray-700 mb-3">Email Tracking</h5>
                <div class="space-y-2">
                  <label class="flex items-center">
                    <%= form.check_box :track_opens,
                        checked: true,
                        class: "rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50" %>
                    <span class="ml-2 text-sm text-gray-700">Track email opens</span>
                  </label>
                  <label class="flex items-center">
                    <%= form.check_box :track_clicks,
                        checked: true,
                        class: "rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50" %>
                    <span class="ml-2 text-sm text-gray-700">Track link clicks</span>
                  </label>
                </div>
              </div>

              <!-- Reply-To Email -->
              <div>
                <%= form.label :reply_to_email, "Reply-To Email (Optional)", class: "block text-sm font-semibold text-gray-700 mb-2" %>
                <%= form.email_field :reply_to_email,
                    placeholder: "<EMAIL>",
                    class: "block w-full px-4 py-3 border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm transition-all duration-200" %>
                <p class="mt-1 text-xs text-gray-500">If different from the sender email</p>
              </div>
            </div>
          </div>

          <!-- Campaign Summary -->
          <div class="bg-indigo-50 rounded-xl p-6 border border-indigo-200">
            <h4 class="text-lg font-semibold text-indigo-900 mb-4">Campaign Summary</h4>
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span class="font-medium text-indigo-700">Recipients:</span>
                <span class="text-indigo-600 ml-1" id="recipient-count">Loading...</span>
              </div>
              <div>
                <span class="font-medium text-indigo-700">Send Time:</span>
                <span class="text-indigo-600 ml-1" id="send-time">Immediately</span>
              </div>
              <div>
                <span class="font-medium text-indigo-700">From:</span>
                <span class="text-indigo-600 ml-1" id="from-info">-</span>
              </div>
              <div>
                <span class="font-medium text-indigo-700">Subject:</span>
                <span class="text-indigo-600 ml-1" id="subject-info">-</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Navigation Buttons -->
    <div class="bg-gray-50 px-8 py-6 border-t border-gray-100 flex items-center justify-between">
      <button type="button"
              data-campaign-wizard-target="prevButton"
              data-action="click->campaign-wizard#previous"
              disabled
              class="inline-flex items-center px-6 py-3 border border-gray-300 rounded-xl shadow-sm text-sm font-semibold text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
        <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
        </svg>
        Previous Step
      </button>

      <div class="flex items-center space-x-4">
        <div class="text-sm text-gray-500">
          <svg class="inline w-4 h-4 mr-1 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>
          Auto-saving draft...
        </div>

        <%= link_to "Save as Draft", "#",
            class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-xl shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200" %>
      </div>

      <button type="button"
              data-campaign-wizard-target="nextButton"
              data-action="click->campaign-wizard#next"
              class="inline-flex items-center px-6 py-3 border border-transparent rounded-xl shadow-sm text-sm font-semibold text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 transform hover:scale-105">
        Next Step
        <svg class="-mr-1 ml-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
        </svg>
      </button>
    </div>

    <!-- Hidden required fields with defaults -->
    <%= form.hidden_field :status, value: "draft" %>
    <%= form.hidden_field :media_type, value: "text" %>
    <%= form.hidden_field :design_theme, value: "modern" %>
    <%= form.hidden_field :font_family, value: "Inter" %>
    <%= form.hidden_field :account_id, value: @current_account&.id %>
    <%= form.hidden_field :user_id, value: current_user&.id %>
  <% end %>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Character counters
    const nameField = document.querySelector('[name="campaign[name]"]')
    const subjectField = document.querySelector('[name="campaign[subject]"]')
    const previewField = document.querySelector('[name="campaign[preview_text]"]')

    function updateCounter(field, counterId) {
      const counter = document.getElementById(counterId)
      if (field && counter) {
        field.addEventListener('input', () => {
          counter.textContent = field.value.length
        })
        // Initialize counter
        counter.textContent = field.value.length
      }
    }

    updateCounter(nameField, 'name-count')
    updateCounter(subjectField, 'subject-count')
    updateCounter(previewField, 'preview-count')

    // Radio button styling
    document.querySelectorAll('input[type="radio"]').forEach(radio => {
      radio.addEventListener('change', function() {
        // Reset all radio buttons in the same group
        document.querySelectorAll(`input[type="radio"][name="${this.name}"]`).forEach(r => {
          const customRadio = r.closest('label').querySelector('.radio-custom div')
          const label = r.closest('label')
          if (customRadio) {
            customRadio.classList.remove('opacity-100')
            customRadio.classList.add('opacity-0')
          }
          label.classList.remove('border-indigo-500', 'border-green-500', 'border-blue-500')
          label.classList.add('border-gray-200')
        })

        // Style selected radio
        const customRadio = this.closest('label').querySelector('.radio-custom div')
        const label = this.closest('label')
        if (customRadio) {
          customRadio.classList.remove('opacity-0')
          customRadio.classList.add('opacity-100')
        }

        // Add appropriate border color based on context
        if (this.value === 'all') {
          label.classList.add('border-indigo-500')
        } else if (this.value === 'subscribed') {
          label.classList.add('border-green-500')
        } else if (this.value === 'tags' || this.value === 'scheduled') {
          label.classList.add('border-blue-500')
        } else {
          label.classList.add('border-indigo-500')
        }

        // Show/hide conditional content
        if (this.name === 'campaign[recipient_type]' && this.value === 'tags') {
          const tagSelection = label.querySelector('.tag-selection')
          if (tagSelection) {
            tagSelection.classList.remove('hidden')
          }
        } else if (this.name === 'campaign[recipient_type]') {
          // Hide all tag selections
          document.querySelectorAll('.tag-selection').forEach(ts => ts.classList.add('hidden'))
        }

        if (this.name === 'campaign[template_choice]' && this.value === 'template') {
          const templateSelection = document.querySelector('.template-selection')
          if (templateSelection) {
            templateSelection.classList.remove('hidden')
          }
        } else if (this.name === 'campaign[template_choice]') {
          const templateSelection = document.querySelector('.template-selection')
          if (templateSelection) {
            templateSelection.classList.add('hidden')
          }
        }

        if (this.name === 'campaign[send_type]' && this.value === 'scheduled') {
          const scheduleDateTime = label.querySelector('.schedule-datetime')
          if (scheduleDateTime) {
            scheduleDateTime.classList.remove('hidden')
          }
        } else if (this.name === 'campaign[send_type]') {
          // Hide all schedule datetime sections
          document.querySelectorAll('.schedule-datetime').forEach(sdt => sdt.classList.add('hidden'))
        }
      })
    })

    // Initialize selected radio buttons
    document.querySelectorAll('input[type="radio"]:checked').forEach(radio => {
      radio.dispatchEvent(new Event('change'))
    })

    // Update campaign summary
    function updateSummary() {
      const recipientType = document.querySelector('[name="campaign[recipient_type]"]:checked')?.value
      const sendType = document.querySelector('[name="campaign[send_type]"]:checked')?.value
      const fromName = document.querySelector('[name="campaign[from_name]"]')?.value
      const fromEmail = document.querySelector('[name="campaign[from_email]"]')?.value
      const subject = document.querySelector('[name="campaign[subject]"]')?.value

      // Update recipient count using preloaded data
      const recipientCount = document.getElementById('recipient-count')
      if (recipientCount) {
        let count = 'Loading...'
        if (recipientType === 'all') count = '<%= @total_contacts_count || 0 %> contacts'
        else if (recipientType === 'subscribed') count = '<%= @subscribed_contacts_count || 0 %> contacts'
        else if (recipientType === 'tags') count = 'Selected tags'
        recipientCount.textContent = count
      }

      // Update send time
      const sendTime = document.getElementById('send-time')
      if (sendTime) {
        sendTime.textContent = sendType === 'now' ? 'Immediately' : 'Scheduled'
      }

      // Update from info
      const fromInfo = document.getElementById('from-info')
      if (fromInfo) {
        fromInfo.textContent = fromName && fromEmail ? `${fromName} <${fromEmail}>` : '-'
      }

      // Update subject info
      const subjectInfo = document.getElementById('subject-info')
      if (subjectInfo) {
        subjectInfo.textContent = subject || '-'
      }
    }

    // Listen for form changes to update summary
    document.addEventListener('input', updateSummary)
    document.addEventListener('change', updateSummary)

    // Initial summary update
    setTimeout(updateSummary, 100)
  })
</script>
