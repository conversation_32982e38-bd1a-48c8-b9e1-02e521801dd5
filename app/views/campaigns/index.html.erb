<% content_for :title, "Campaigns - RapidMarkt" %>

<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
  <!-- Enhanced Header Section -->
  <div class="relative bg-white/80 backdrop-blur-lg shadow-xl border-b border-white/30">
    <div class="absolute inset-0 bg-gradient-to-r from-indigo-600/5 via-purple-600/5 to-pink-600/5"></div>
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="py-8">
        <!-- Enhanced Breadcrumb -->
        <nav class="flex mb-6" aria-label="Breadcrumb">
          <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
              <%= link_to root_path, class: "inline-flex items-center text-sm font-medium text-gray-500 hover:text-indigo-600 transition-colors duration-200" do %>
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                </svg>
                Dashboard
              <% end %>
            </li>
            <li>
              <div class="flex items-center">
                <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                </svg>
                <span class="ml-1 text-sm font-medium text-gray-700 md:ml-2">Campaigns</span>
              </div>
            </li>
          </ol>
        </nav>
        
        <div class="md:flex md:items-center md:justify-between">
          <div class="flex-1 min-w-0">
            <div class="flex items-center space-x-3 mb-4">
              <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                  <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
              </div>
              <div>
                <h1 class="text-3xl font-bold bg-gradient-to-r from-gray-900 via-indigo-800 to-purple-800 bg-clip-text text-transparent sm:text-4xl">
                  Email Campaigns
                </h1>
                <p class="mt-2 text-lg text-gray-600">
                  Create, manage, and track your email marketing campaigns with powerful analytics
                </p>
              </div>
            </div>
          </div>
          <div class="mt-8 lg:mt-0 lg:ml-6 flex space-x-4">
            <%= link_to new_campaign_path, 
                class: "inline-flex items-center px-6 py-3 border border-transparent rounded-xl shadow-lg text-sm font-medium text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 hover:shadow-xl hover:-translate-y-0.5" do %>
              <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              New Campaign
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

    <!-- Enhanced Search and Filters -->
    <div class="bg-white/80 backdrop-blur-lg shadow-2xl rounded-2xl border border-white/30 p-8 mb-10 hover:shadow-3xl transition-all duration-500">
      <div class="mb-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-2 flex items-center">
          <svg class="w-5 h-5 text-indigo-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
          </svg>
          Filter Your Campaigns
        </h3>
        <p class="text-sm text-gray-600">Search and filter through your email campaigns by status and content</p>
      </div>
      
      <%= form_with url: campaigns_path, method: :get, local: true, class: "space-y-6" do |form| %>
        <div class="grid grid-cols-1 lg:grid-cols-12 gap-4 items-end">
          <!-- Search Input -->
          <div class="lg:col-span-6">
            <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Search Campaigns</label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <%= form.text_field :search, 
                  value: params[:search],
                  placeholder: "Search by name, subject, or content...",
                  class: "pl-10 py-3 px-4 block w-full border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm transition-all duration-200 bg-gray-50/50 hover:bg-white focus:bg-white" %>
            </div>
          </div>
          
          <!-- Status Filter -->
          <div class="lg:col-span-3">
            <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Campaign Status</label>
            <%= form.select :status, 
                options_for_select([
                  ['All Statuses', ''],
                  ['Draft', 'draft'],
                  ['Scheduled', 'scheduled'],
                  ['Sending', 'sending'],
                  ['Sent', 'sent']
                ], params[:status]),
                {},
                { class: "py-3 px-4 border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm transition-all duration-200 bg-gray-50/50 hover:bg-white focus:bg-white w-full" } %>
          </div>
          
          <!-- Action Buttons -->
          <div class="lg:col-span-3 flex space-x-3">
            <%= form.submit "Filter", 
                class: "flex-1 inline-flex items-center justify-center px-6 py-3 border border-transparent rounded-xl shadow-lg text-sm font-medium text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 hover:shadow-xl hover:-translate-y-0.5" %>
            <% if params[:search].present? || params[:status].present? %>
              <%= link_to campaigns_path, 
                  class: "inline-flex items-center justify-center px-4 py-3 border border-gray-200 rounded-xl shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 hover:shadow-md" do %>
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              <% end %>
            <% end %>
          </div>
        </div>
        
        <!-- Active Filters Display -->
        <% if params[:search].present? || params[:status].present? %>
          <div class="flex items-center space-x-2 pt-4 border-t border-gray-100">
            <span class="text-sm text-gray-500">Active filters:</span>
            <% if params[:search].present? %>
              <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                Search: "<%= params[:search] %>"
              </span>
            <% end %>
            <% if params[:status].present? %>
              <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                Status: <%= params[:status].humanize %>
              </span>
            <% end %>
          </div>
        <% end %>
      <% end %>
    </div>

    <!-- Enhanced Bulk Actions -->
    <% if @campaigns.any? %>
      <div class="bg-white/90 backdrop-blur-sm shadow-xl rounded-2xl border border-white/40 p-6 mb-8 hover:shadow-2xl transition-all duration-300" data-controller="bulk-actions">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div class="flex items-center space-x-6">
            <label class="flex items-center group cursor-pointer">
              <div class="relative">
                <input type="checkbox" data-action="change->bulk-actions#toggleAll" data-bulk-actions-target="selectAll" class="w-5 h-5 rounded-lg border-2 border-gray-300 text-indigo-600 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-all duration-200">
                <div class="absolute inset-0 rounded-lg bg-gradient-to-r from-indigo-500 to-purple-600 opacity-0 group-hover:opacity-10 transition-opacity duration-200"></div>
              </div>
              <span class="ml-3 text-sm font-medium text-gray-700 group-hover:text-indigo-600 transition-colors duration-200">Select All Campaigns</span>
            </label>
            <div class="flex items-center space-x-2">
              <div class="w-2 h-2 bg-indigo-400 rounded-full animate-pulse"></div>
              <span class="text-sm text-gray-500 font-medium" data-bulk-actions-target="selectedCount">0 campaigns selected</span>
            </div>
          </div>
          
          <div class="flex items-center space-x-3" data-bulk-actions-target="actions" style="display: none;">
            <%= button_to "Send Selected", bulk_send_campaigns_path, 
                params: { campaign_ids: [] },
                data: { 
                  turbo_method: :post, 
                  turbo_confirm: "Are you sure you want to send the selected campaigns? This action cannot be undone.",
                  bulk_actions_target: "sendButton"
                },
                class: "inline-flex items-center px-4 py-2.5 border border-transparent text-sm font-medium rounded-xl text-white bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5" do %>
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
              </svg>
              Send Selected
            <% end %>
            <%= button_to "Schedule Selected", bulk_schedule_campaigns_path,
                params: { campaign_ids: [] },
                data: { 
                  turbo_method: :post,
                  bulk_actions_target: "scheduleButton"
                },
                class: "inline-flex items-center px-4 py-2.5 border border-gray-200 text-sm font-medium rounded-xl text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 hover:shadow-md" do %>
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Schedule Selected
            <% end %>
          </div>
        </div>
      </div>
    <% end %>

    <!-- Campaigns list -->
    <div class="bg-white shadow-lg rounded-xl border border-gray-100 overflow-hidden">
    <% if @campaigns.any? %>
      <ul role="list" class="divide-y divide-gray-200">
        <% @campaigns.each do |campaign| %>
          <li data-controller="dropdown">
            <div class="px-4 py-4 sm:px-6">
              <div class="flex items-center justify-between">
                <div class="flex items-center min-w-0 flex-1">
                  <div class="flex-shrink-0 mr-4">
                    <input type="checkbox" 
                           data-action="change->bulk-actions#updateSelection" 
                           data-bulk-actions-target="campaignCheckbox" 
                           data-campaign-id="<%= campaign.id %>"
                           data-campaign-status="<%= campaign.status %>"
                           class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                  </div>
                  <div class="flex-shrink-0">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium 
                      <%= case campaign.status
                          when 'draft' then 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-800'
                          when 'sending' then 'bg-gradient-to-r from-yellow-100 to-orange-200 text-yellow-800'
                          when 'sent' then 'bg-gradient-to-r from-green-100 to-emerald-200 text-green-800'
                          when 'scheduled' then 'bg-gradient-to-r from-blue-100 to-indigo-200 text-blue-800'
                          else 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-800'
                          end %>">
                      <%= campaign.status.humanize %>
                    </span>
                  </div>
                  <div class="ml-4 min-w-0 flex-1">
                    <div class="flex items-center">
                      <p class="text-sm font-medium text-indigo-600 truncate">
                        <%= link_to campaign.name, campaign_path(campaign), class: "hover:text-indigo-900" %>
                      </p>
                    </div>
                    <div class="mt-1">
                      <p class="text-sm text-gray-900 truncate">
                        Subject: <%= campaign.subject %>
                      </p>
                      <div class="mt-1 flex items-center text-sm text-gray-500">
                        <p>
                          Created <%= time_ago_in_words(campaign.created_at) %> ago
                        </p>
                        <% if campaign.scheduled_at.present? %>
                          <span class="mx-2">•</span>
                          <p>
                            Scheduled for <%= campaign.scheduled_at.strftime("%B %d, %Y at %I:%M %p") %>
                          </p>
                        <% end %>
                        <% if campaign.sent_at.present? %>
                          <span class="mx-2">•</span>
                          <p>
                            Sent <%= time_ago_in_words(campaign.sent_at) %> ago
                          </p>
                        <% end %>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="flex items-center space-x-4">
                  <% if campaign.sent? %>
                    <div class="text-right">
                      <p class="text-sm text-gray-900">
                        <%= campaign.campaign_contacts.where.not(sent_at: nil).count %> sent
                      </p>
                      <p class="text-sm text-gray-500">
                        <%= campaign.campaign_contacts.where.not(opened_at: nil).count %> opened
                      </p>
                    </div>
                  <% end %>
                  
                  <!-- Actions dropdown -->
                  <div class="relative">
                    <button type="button" 
                            data-action="click->dropdown#toggle"
                            class="inline-flex items-center p-2 border border-transparent rounded-full shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                      <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                      </svg>
                    </button>
                    
                    <div data-dropdown-target="menu" 
                         class="hidden origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-10">
                      <div class="py-1">
                        <%= link_to "View", campaign_path(campaign), 
                            class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" %>
                        <%= link_to "Edit", edit_campaign_path(campaign), 
                            class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" %>
                        <%= link_to "Preview", preview_campaign_path(campaign), 
                            class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",
                            target: "_blank" %>
                        <% if campaign.draft? %>
                          <%= link_to "Send Test", send_test_campaign_path(campaign), 
                              data: { turbo_method: :post },
                              class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" %>
                        <% end %>
                        <% unless campaign.sent? %>
                          <%= link_to "Delete", campaign_path(campaign), 
                              data: { turbo_method: :delete, turbo_confirm: "Are you sure you want to delete this campaign?" },
                              class: "block px-4 py-2 text-sm text-red-700 hover:bg-red-100" %>
                        <% end %>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </li>
        <% end %>
      </ul>
      
      <!-- Pagination -->
      <% if @campaigns.respond_to?(:total_pages) %>
        <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
          <div class="flex items-center justify-between">
            <div class="flex-1 flex justify-between sm:hidden">
              <% if @campaigns.prev_page %>
                <%= link_to "Previous", campaigns_path(page: @campaigns.prev_page, search: params[:search], status: params[:status]), 
                    class: "relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50" %>
              <% end %>
              <% if @campaigns.next_page %>
                <%= link_to "Next", campaigns_path(page: @campaigns.next_page, search: params[:search], status: params[:status]), 
                    class: "ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50" %>
              <% end %>
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p class="text-sm text-gray-700">
                  Showing
                  <span class="font-medium"><%= (@campaigns.current_page - 1) * @campaigns.limit_value + 1 %></span>
                  to
                  <span class="font-medium"><%= [@campaigns.current_page * @campaigns.limit_value, @campaigns.total_count].min %></span>
                  of
                  <span class="font-medium"><%= @campaigns.total_count %></span>
                  results
                </p>
              </div>
              <div>
                <%= paginate @campaigns if defined?(Kaminari) %>
              </div>
            </div>
          </div>
        </div>
      <% end %>
    <% else %>
      <div class="text-center py-16">
        <div class="w-20 h-20 bg-gradient-to-r from-indigo-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <svg class="h-10 w-10 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 7.89a2 2 0 002.83 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">
          <% if params[:search].present? || params[:status].present? %>
            No campaigns found 🔍
          <% else %>
            No campaigns yet 📧
          <% end %>
        </h3>
        <p class="text-sm text-gray-500 mb-8 max-w-sm mx-auto">
          <% if params[:search].present? || params[:status].present? %>
            Try adjusting your search or filter criteria to find what you're looking for.
          <% else %>
            Get started by creating your first email campaign and reach your audience effectively.
          <% end %>
        </p>
        <div>
          <% if params[:search].present? || params[:status].present? %>
            <%= link_to "Clear filters", campaigns_path, 
                class: "inline-flex items-center px-6 py-3 border border-transparent shadow-sm text-sm font-medium rounded-lg text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-200" %>
          <% else %>
            <%= link_to new_campaign_path, 
                class: "inline-flex items-center px-6 py-3 border border-transparent shadow-sm text-sm font-medium rounded-lg text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-200" do %>
              <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Create Your First Campaign
            <% end %>
          <% end %>
        </div>
      </div>
    <% end %>
    </div>
  </div>
</div>