<% content_for :title, "Campaigns - RapidMarkt" %>

<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
  <!-- Enhanced Header Section -->
  <div class="relative bg-white/80 backdrop-blur-lg shadow-xl border-b border-white/30">
    <div class="absolute inset-0 bg-gradient-to-r from-indigo-600/5 via-purple-600/5 to-pink-600/5"></div>
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="py-8">
        <!-- Enhanced Breadcrumb -->
        <nav class="flex mb-6" aria-label="Breadcrumb">
          <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
              <%= link_to root_path, class: "inline-flex items-center text-sm font-medium text-gray-500 hover:text-indigo-600 transition-colors duration-200" do %>
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                </svg>
                Dashboard
              <% end %>
            </li>
            <li>
              <div class="flex items-center">
                <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                </svg>
                <span class="ml-1 text-sm font-medium text-gray-700 md:ml-2">Campaigns</span>
              </div>
            </li>
          </ol>
        </nav>
        
        <div class="md:flex md:items-center md:justify-between">
          <div class="flex-1 min-w-0">
            <div class="flex items-center space-x-3 mb-4">
              <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                  <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
              </div>
              <div>
                <h1 class="text-3xl font-bold bg-gradient-to-r from-gray-900 via-indigo-800 to-purple-800 bg-clip-text text-transparent sm:text-4xl">
                  Email Campaigns
                </h1>
                <p class="mt-2 text-lg text-gray-600">
                  Create, manage, and track your email marketing campaigns with powerful analytics
                </p>
              </div>
            </div>
          </div>
          <div class="mt-8 lg:mt-0 lg:ml-6 flex space-x-4">
            <%= link_to new_campaign_path, 
                class: "inline-flex items-center px-6 py-3 border border-transparent rounded-xl shadow-lg text-sm font-medium text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 hover:shadow-xl hover:-translate-y-0.5" do %>
              <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              New Campaign
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

    <!-- Enhanced Search and Filters -->
    <div class="bg-white/80 backdrop-blur-lg shadow-2xl rounded-2xl border border-white/30 p-8 mb-10 hover:shadow-3xl transition-all duration-500">
      <div class="mb-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-2 flex items-center">
          <svg class="w-5 h-5 text-indigo-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
          </svg>
          Filter Your Campaigns
        </h3>
        <p class="text-sm text-gray-600">Search and filter through your email campaigns by status and content</p>
      </div>
      
      <%= form_with url: campaigns_path, method: :get, local: true, class: "space-y-6" do |form| %>
        <div class="grid grid-cols-1 lg:grid-cols-12 gap-4 items-end">
          <!-- Search Input -->
          <div class="lg:col-span-6">
            <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Search Campaigns</label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <%= form.text_field :search, 
                  value: params[:search],
                  placeholder: "Search by name, subject, or content...",
                  class: "pl-10 py-3 px-4 block w-full border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm transition-all duration-200 bg-gray-50/50 hover:bg-white focus:bg-white" %>
            </div>
          </div>
          
          <!-- Status Filter -->
          <div class="lg:col-span-3">
            <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Campaign Status</label>
            <%= form.select :status, 
                options_for_select([
                  ['All Statuses', ''],
                  ['Draft', 'draft'],
                  ['Scheduled', 'scheduled'],
                  ['Sending', 'sending'],
                  ['Sent', 'sent']
                ], params[:status]),
                {},
                { class: "py-3 px-4 border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm transition-all duration-200 bg-gray-50/50 hover:bg-white focus:bg-white w-full" } %>
          </div>
          
          <!-- Action Buttons -->
          <div class="lg:col-span-3 flex space-x-3">
            <%= form.submit "Filter", 
                class: "flex-1 inline-flex items-center justify-center px-6 py-3 border border-transparent rounded-xl shadow-lg text-sm font-medium text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 hover:shadow-xl hover:-translate-y-0.5" %>
            <% if params[:search].present? || params[:status].present? %>
              <%= link_to campaigns_path, 
                  class: "inline-flex items-center justify-center px-4 py-3 border border-gray-200 rounded-xl shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 hover:shadow-md" do %>
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              <% end %>
            <% end %>
          </div>
        </div>
        
        <!-- Active Filters Display -->
        <% if params[:search].present? || params[:status].present? %>
          <div class="flex items-center space-x-2 pt-4 border-t border-gray-100">
            <span class="text-sm text-gray-500">Active filters:</span>
            <% if params[:search].present? %>
              <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                Search: "<%= params[:search] %>"
              </span>
            <% end %>
            <% if params[:status].present? %>
              <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                Status: <%= params[:status].humanize %>
              </span>
            <% end %>
          </div>
        <% end %>
      <% end %>
    </div>

    <!-- Enhanced Bulk Actions -->
    <% if @campaigns.any? %>
      <div class="bg-white/90 backdrop-blur-sm shadow-xl rounded-2xl border border-white/40 p-6 mb-8 hover:shadow-2xl transition-all duration-300" data-controller="bulk-actions">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div class="flex items-center space-x-6">
            <label class="flex items-center group cursor-pointer">
              <div class="relative">
                <input type="checkbox" data-action="change->bulk-actions#toggleAll" data-bulk-actions-target="selectAll" class="w-5 h-5 rounded-lg border-2 border-gray-300 text-indigo-600 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-all duration-200">
                <div class="absolute inset-0 rounded-lg bg-gradient-to-r from-indigo-500 to-purple-600 opacity-0 group-hover:opacity-10 transition-opacity duration-200"></div>
              </div>
              <span class="ml-3 text-sm font-medium text-gray-700 group-hover:text-indigo-600 transition-colors duration-200">Select All Campaigns</span>
            </label>
            <div class="flex items-center space-x-2">
              <div class="w-2 h-2 bg-indigo-400 rounded-full animate-pulse"></div>
              <span class="text-sm text-gray-500 font-medium" data-bulk-actions-target="selectedCount">0 campaigns selected</span>
            </div>
          </div>
          
          <div class="flex items-center space-x-3" data-bulk-actions-target="actions" style="display: none;">
            <%= button_to  bulk_send_campaigns_path, 
                params: { campaign_ids: [] },
                data: { 
                  turbo_method: :post, 
                  turbo_confirm: "Are you sure you want to send the selected campaigns? This action cannot be undone.",
                  bulk_actions_target: "sendButton"
                },
                class: "inline-flex items-center px-4 py-2.5 border border-transparent text-sm font-medium rounded-xl text-white bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5" do %>
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
              </svg>
              Send Selected
            <% end %>
            <%= button_to  bulk_schedule_campaigns_path,
                params: { campaign_ids: [] },
                data: { 
                  turbo_method: :post,
                  bulk_actions_target: "scheduleButton"
                },
                class: "inline-flex items-center px-4 py-2.5 border border-gray-200 text-sm font-medium rounded-xl text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 hover:shadow-md" do %>
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Schedule Selected
            <% end %>
          </div>
        </div>
      </div>
    <% end %>

    <!-- Campaigns list -->
    <div class="space-y-4">
    <% if @campaigns.any? %>
      <% @campaigns.each do |campaign| %>
        <div data-controller="dropdown" class="bg-white/80 backdrop-blur-sm shadow-lg rounded-2xl border border-gray-100/50 overflow-hidden hover:shadow-xl transition-all duration-300 hover:border-indigo-200/50">
          <div class="px-6 py-5">
            <div class="flex items-start justify-between">
              <div class="flex items-start min-w-0 flex-1 space-x-4">
                <!-- Checkbox -->
                <div class="flex-shrink-0 pt-1">
                  <input type="checkbox" 
                         data-action="change->bulk-actions#updateSelection" 
                         data-bulk-actions-target="campaignCheckbox" 
                         data-campaign-id="<%= campaign.id %>"
                         data-campaign-status="<%= campaign.status %>"
                         class="h-5 w-5 rounded-lg border-2 border-gray-300 text-indigo-600 focus:ring-indigo-500 focus:ring-2 focus:ring-offset-2 transition-all duration-200">
                </div>
                
                <!-- Campaign Icon -->
                <div class="flex-shrink-0 pt-1">
                  <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                    <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 7.89a2 2 0 002.83 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                </div>
                
                <!-- Campaign Details -->
                <div class="min-w-0 flex-1">
                  <div class="flex items-center space-x-3 mb-2">
                    <h3 class="text-lg font-semibold text-gray-900 truncate">
                      <%= link_to campaign.name, campaign_path(campaign), class: "hover:text-indigo-600 transition-colors duration-200" %>
                    </h3>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold shadow-sm
                      <%= case campaign.status
                          when 'draft' then 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-800 border border-gray-300'
                          when 'sending' then 'bg-gradient-to-r from-yellow-100 to-orange-200 text-yellow-800 border border-yellow-300'
                          when 'sent' then 'bg-gradient-to-r from-green-100 to-emerald-200 text-green-800 border border-green-300'
                          when 'scheduled' then 'bg-gradient-to-r from-blue-100 to-indigo-200 text-blue-800 border border-blue-300'
                          else 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-800 border border-gray-300'
                          end %>">
                      <div class="w-2 h-2 rounded-full mr-2 <%= case campaign.status
                          when 'draft' then 'bg-gray-500'
                          when 'sending' then 'bg-yellow-500 animate-pulse'
                          when 'sent' then 'bg-green-500'
                          when 'scheduled' then 'bg-blue-500'
                          else 'bg-gray-500'
                          end %>"></div>
                      <%= campaign.status.humanize %>
                    </span>
                  </div>
                  
                  <div class="space-y-2">
                    <p class="text-sm text-gray-700 font-medium">
                      <span class="text-gray-500">Subject:</span> <%= campaign.subject %>
                    </p>
                    
                    <div class="flex flex-wrap items-center gap-4 text-sm text-gray-500">
                      <div class="flex items-center">
                        <svg class="h-4 w-4 mr-1.5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Created <%= time_ago_in_words(campaign.created_at) %> ago
                      </div>
                      
                      <% if campaign.scheduled_at.present? %>
                        <div class="flex items-center">
                          <svg class="h-4 w-4 mr-1.5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                          Scheduled for <%= campaign.scheduled_at.strftime("%B %d, %Y at %I:%M %p") %>
                        </div>
                      <% end %>
                      
                      <% if campaign.sent_at.present? %>
                        <div class="flex items-center">
                          <svg class="h-4 w-4 mr-1.5 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                          </svg>
                          Sent <%= time_ago_in_words(campaign.sent_at) %> ago
                        </div>
                      <% end %>
                     </div>
                   </div>
                 </div>
               </div>
               
               <!-- Statistics and Actions -->
               <div class="flex items-center space-x-6">
                 <% if campaign.sent? %>
                   <div class="text-right space-y-1">
                     <div class="flex items-center justify-end space-x-2">
                       <svg class="h-4 w-4 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                         <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                       </svg>
                       <span class="text-sm font-semibold text-gray-900">
                         <%= campaign.campaign_contacts.where.not(sent_at: nil).count %>
                       </span>
                       <span class="text-xs text-gray-500">sent</span>
                     </div>
                     <div class="flex items-center justify-end space-x-2">
                       <svg class="h-4 w-4 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                         <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                         <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                       </svg>
                       <span class="text-sm font-semibold text-gray-900">
                         <%= campaign.campaign_contacts.where.not(opened_at: nil).count %>
                       </span>
                       <span class="text-xs text-gray-500">opened</span>
                     </div>
                   </div>
                 <% end %>
                 
                 <!-- Actions dropdown -->
                 <div class="relative">
                   <button type="button" 
                           data-action="click->dropdown#toggle"
                           class="inline-flex items-center p-3 border border-gray-200 rounded-xl shadow-sm text-gray-600 bg-white/80 backdrop-blur-sm hover:bg-gray-50 hover:border-indigo-300 hover:text-indigo-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200">
                     <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                       <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                     </svg>
                   </button>
                   
                   <div data-dropdown-target="menu" 
                        class="hidden origin-top-right absolute right-0 mt-2 w-56 rounded-xl shadow-xl bg-white/95 backdrop-blur-sm ring-1 ring-black ring-opacity-5 focus:outline-none z-10 border border-gray-100">
                     <div class="py-2">
                       <%= link_to campaign_path(campaign), 
                           class: "flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 transition-colors duration-200" do %>
                         <svg class="h-4 w-4 mr-3 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                           <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                           <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                         </svg>
                         View Campaign
                       <% end %>
                       <%= link_to edit_campaign_path(campaign), 
                           class: "flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 transition-colors duration-200" do %>
                         <svg class="h-4 w-4 mr-3 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                           <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                         </svg>
                         Edit Campaign
                       <% end %>
                       <%= link_to preview_campaign_path(campaign), 
                           target: "_blank",
                           class: "flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 transition-colors duration-200" do %>
                         <svg class="h-4 w-4 mr-3 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                           <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                         </svg>
                         Preview
                       <% end %>
                       <% if campaign.draft? %>
                         <%= link_to send_test_campaign_path(campaign), 
                             data: { turbo_method: :post },
                             class: "flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 transition-colors duration-200" do %>
                           <svg class="h-4 w-4 mr-3 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                             <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                           </svg>
                           Send Test
                         <% end %>
                       <% end %>
                       <% unless campaign.sent? %>
                         <div class="border-t border-gray-100 my-1"></div>
                         <%= link_to campaign_path(campaign), 
                             data: { turbo_method: :delete, turbo_confirm: "Are you sure you want to delete this campaign?" },
                             class: "flex items-center px-4 py-3 text-sm text-red-600 hover:bg-red-50 transition-colors duration-200" do %>
                           <svg class="h-4 w-4 mr-3 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                             <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                           </svg>
                           Delete Campaign
                         <% end %>
                       <% end %>
                     </div>
                   </div>
                 </div>
               </div>
             </div>
           </div>
         </div>
       <% end %>
      
      <!-- Pagination -->
      <% if @campaigns.respond_to?(:total_pages) && @campaigns.total_pages > 1 %>
        <div class="mt-8">
          <div class="bg-white/80 backdrop-blur-sm shadow-lg rounded-2xl border border-gray-100/50 px-6 py-4">
            <div class="flex items-center justify-between">
              <div class="flex-1 flex justify-between sm:hidden">
                <% if @campaigns.prev_page %>
                  <%= link_to campaigns_path(page: @campaigns.prev_page, search: params[:search], status: params[:status]), 
                      class: "relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-xl text-gray-700 bg-white/80 backdrop-blur-sm hover:bg-gray-50 hover:border-indigo-300 transition-all duration-200" do %>
                    <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                    </svg>
                    Previous
                  <% end %>
                <% end %>
                <% if @campaigns.next_page %>
                  <%= link_to campaigns_path(page: @campaigns.next_page, search: params[:search], status: params[:status]), 
                      class: "ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-xl text-gray-700 bg-white/80 backdrop-blur-sm hover:bg-gray-50 hover:border-indigo-300 transition-all duration-200" do %>
                    Next
                    <svg class="h-4 w-4 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                  <% end %>
                <% end %>
              </div>
              <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p class="text-sm text-gray-600">
                    Showing
                    <span class="font-semibold text-gray-900"><%= (@campaigns.current_page - 1) * @campaigns.limit_value + 1 %></span>
                    to
                    <span class="font-semibold text-gray-900"><%= [@campaigns.current_page * @campaigns.limit_value, @campaigns.total_count].min %></span>
                    of
                    <span class="font-semibold text-gray-900"><%= @campaigns.total_count %></span>
                    campaigns
                  </p>
                </div>
                <div>
                  <%= paginate @campaigns, theme: 'twitter_bootstrap_4' if defined?(Kaminari) %>
                </div>
              </div>
            </div>
          </div>
        </div>
      <% end %>
    <% else %>
      <!-- Empty State -->
      <div class="bg-white/80 backdrop-blur-sm shadow-lg rounded-2xl border border-gray-100/50 overflow-hidden">
        <div class="text-center py-20 px-6">
          <div class="w-24 h-24 bg-gradient-to-br from-indigo-100 via-purple-50 to-pink-100 rounded-2xl flex items-center justify-center mx-auto mb-8 shadow-lg">
            <svg class="h-12 w-12 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M3 8l7.89 7.89a2 2 0 002.83 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
          
          <div class="max-w-md mx-auto">
            <h3 class="text-2xl font-bold text-gray-900 mb-3">
              <% if params[:search].present? || params[:status].present? %>
                No campaigns found 🔍
              <% else %>
                Ready to launch? 🚀
              <% end %>
            </h3>
            
            <p class="text-gray-600 mb-8 leading-relaxed">
              <% if params[:search].present? || params[:status].present? %>
                We couldn't find any campaigns matching your criteria. Try adjusting your search or filter settings.
              <% else %>
                Create your first email campaign and start engaging with your audience. It's easier than you think!
              <% end %>
            </p>
            
            <div class="space-y-4">
              <% if params[:search].present? || params[:status].present? %>
                <%= link_to campaigns_path, 
                    class: "inline-flex items-center px-8 py-4 border border-transparent shadow-lg text-base font-semibold rounded-2xl text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-300 transform hover:scale-105" do %>
                  <svg class="-ml-1 mr-3 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  Clear All Filters
                <% end %>
              <% else %>
                <%= link_to new_campaign_path, 
                    class: "inline-flex items-center px-8 py-4 border border-transparent shadow-lg text-base font-semibold rounded-2xl text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-300 transform hover:scale-105" do %>
                  <svg class="-ml-1 mr-3 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Create Your First Campaign
                <% end %>
              <% end %>
            </div>
            
            <% unless params[:search].present? || params[:status].present? %>
              <div class="mt-8 pt-8 border-t border-gray-200">
                <p class="text-sm text-gray-500 mb-4">Need help getting started?</p>
                <div class="flex justify-center space-x-6 text-sm">
                  <a href="#" class="text-indigo-600 hover:text-indigo-500 font-medium transition-colors duration-200">
                    📖 View Guide
                  </a>
                  <a href="#" class="text-indigo-600 hover:text-indigo-500 font-medium transition-colors duration-200">
                    🎥 Watch Tutorial
                  </a>
                  <a href="#" class="text-indigo-600 hover:text-indigo-500 font-medium transition-colors duration-200">
                    💬 Get Support
                  </a>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    <% end %>
    </div>
  </div>
</div>