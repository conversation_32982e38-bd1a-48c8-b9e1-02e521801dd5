<% content_for :title, "#{@campaign.name} - RapidMarkt" %>

<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
  <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
    <!-- Page header -->
    <div class="mb-8">
      <!-- Enhanced Breadcrumb -->
      <nav class="flex mb-6" aria-label="Breadcrumb">
        <ol role="list" class="flex items-center space-x-4">
          <li>
            <div>
              <%= link_to campaigns_path, class: "group flex items-center text-slate-400 hover:text-indigo-600 transition-colors duration-200" do %>
                <svg class="flex-shrink-0 h-5 w-5 group-hover:scale-110 transition-transform duration-200" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                </svg>
                <span class="sr-only">Back</span>
              <% end %>
            </div>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="flex-shrink-0 h-5 w-5 text-slate-300" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
              </svg>
              <%= link_to "Campaigns", campaigns_path, class: "ml-4 text-sm font-medium text-slate-500 hover:text-indigo-600 transition-colors duration-200" %>
            </div>
          </li>
          <li>
            <div class="flex items-center">
              <svg class="flex-shrink-0 h-5 w-5 text-slate-300" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
              </svg>
              <span class="ml-4 text-sm font-medium text-slate-600"><%= truncate(@campaign.name, length: 30) %></span>
            </div>
          </li>
        </ol>
      </nav>
      
      <!-- Enhanced Header Card -->
      <div class="bg-white/70 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-8">
        <div class="flex items-start justify-between">
          <div class="flex-1">
            <div class="flex items-center space-x-3 mb-4">
              <div class="p-3 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl shadow-lg">
                <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <div>
                <h1 class="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent"><%= @campaign.name %></h1>
                <p class="mt-1 text-slate-600 font-medium">
                  <span class="inline-flex items-center">
                    <svg class="h-4 w-4 mr-2 text-slate-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-1l-4 4z" />
                    </svg>
                    <%= @campaign.subject %>
                  </span>
                </p>
              </div>
            </div>
          </div>
          <div class="flex items-center space-x-4">
            <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold shadow-lg
              <%= case @campaign.status
                  when 'draft' then 'bg-gradient-to-r from-slate-100 to-slate-200 text-slate-800 border border-slate-300'
                  when 'sending' then 'bg-gradient-to-r from-amber-100 to-yellow-200 text-amber-800 border border-amber-300'
                  when 'sent' then 'bg-gradient-to-r from-emerald-100 to-green-200 text-emerald-800 border border-emerald-300'
                  when 'scheduled' then 'bg-gradient-to-r from-blue-100 to-indigo-200 text-blue-800 border border-blue-300'
                  else 'bg-gradient-to-r from-slate-100 to-slate-200 text-slate-800 border border-slate-300'
                  end %>">
              <div class="w-2 h-2 rounded-full mr-2 shadow-sm
                <%= case @campaign.status
                    when 'draft' then 'bg-slate-500'
                    when 'sending' then 'bg-amber-500'
                    when 'sent' then 'bg-emerald-500'
                    when 'scheduled' then 'bg-blue-500'
                    else 'bg-slate-500'
                    end %>"></div>
              <%= @campaign.status.humanize %>
            </span>
            
            <div class="relative" data-controller="dropdown">
              <button type="button" 
                      data-action="click->dropdown#toggle"
                      class="inline-flex items-center px-6 py-3 bg-white/80 backdrop-blur-sm border border-slate-200 rounded-xl shadow-lg text-sm font-medium text-slate-700 hover:bg-white hover:shadow-xl hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200">
                <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                </svg>
                Actions
                <svg class="-mr-1 ml-2 h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
              </button>
              
              <div data-dropdown-target="menu" 
                   class="hidden origin-top-right absolute right-0 mt-2 w-56 bg-white/90 backdrop-blur-xl rounded-xl shadow-2xl border border-white/20 focus:outline-none z-10 overflow-hidden">
                <div class="py-2">
                  <% unless @campaign.sent? %>
                    <%= link_to edit_campaign_path(@campaign), 
                        class: "group flex items-center px-4 py-3 text-sm text-slate-700 hover:bg-indigo-50 hover:text-indigo-900 transition-colors duration-200" do %>
                      <svg class="mr-3 h-4 w-4 text-slate-400 group-hover:text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                      Edit Campaign
                    <% end %>
                  <% end %>
                  <%= link_to preview_campaign_path(@campaign), 
                      target: "_blank",
                      class: "group flex items-center px-4 py-3 text-sm text-slate-700 hover:bg-indigo-50 hover:text-indigo-900 transition-colors duration-200" do %>
                    <svg class="mr-3 h-4 w-4 text-slate-400 group-hover:text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                    Preview Email
                  <% end %>
                  <% if @campaign.draft? %>
                    <%= link_to send_test_campaign_path(@campaign), 
                        data: { turbo_method: :post },
                        class: "group flex items-center px-4 py-3 text-sm text-slate-700 hover:bg-indigo-50 hover:text-indigo-900 transition-colors duration-200" do %>
                      <svg class="mr-3 h-4 w-4 text-slate-400 group-hover:text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                      </svg>
                      Send Test Email
                    <% end %>
                    <% recipient_count = case @campaign.recipient_type
                                         when 'all'
                                           @campaign.account.contacts.count
                                         when 'subscribed'
                                           @campaign.account.contacts.where(status: 'subscribed').count
                                         when 'tags'
                                           @campaign.tags.any? ? @campaign.tags.joins(:contacts).distinct.count('contacts.id') : 0
                                         else
                                           @campaign.account.contacts.where(status: 'subscribed').count
                                         end %>
                    <%= link_to send_campaign_campaign_path(@campaign), 
                        data: { turbo_method: :post, turbo_confirm: "Are you sure you want to send this campaign to #{number_with_delimiter(recipient_count)} recipient#{'s' if recipient_count != 1}? This action cannot be undone." },
                        class: "group flex items-center px-4 py-3 text-sm text-emerald-700 hover:bg-emerald-50 hover:text-emerald-900 transition-colors duration-200" do %>
                      <svg class="mr-3 h-4 w-4 text-emerald-500 group-hover:text-emerald-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                      </svg>
                      Send Campaign
                    <% end %>
                  <% end %>
                  <%= link_to new_campaign_path(duplicate: @campaign.id), 
                      class: "group flex items-center px-4 py-3 text-sm text-slate-700 hover:bg-indigo-50 hover:text-indigo-900 transition-colors duration-200" do %>
                    <svg class="mr-3 h-4 w-4 text-slate-400 group-hover:text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                    Duplicate Campaign
                  <% end %>
                  <% unless @campaign.sent? %>
                    <div class="border-t border-slate-200 my-1"></div>
                    <%= link_to campaign_path(@campaign), 
                        data: { turbo_method: :delete, turbo_confirm: "Are you sure you want to delete this campaign?" },
                        class: "group flex items-center px-4 py-3 text-sm text-red-700 hover:bg-red-50 hover:text-red-900 transition-colors duration-200" do %>
                      <svg class="mr-3 h-4 w-4 text-red-500 group-hover:text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                      Delete Campaign
                    <% end %>
                  <% end %>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

    <!-- Enhanced Campaign Stats -->
    <% if @campaign.sent? %>
      <div class="mb-8">
        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          <!-- Total Sent -->
          <div class="group bg-white/70 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:scale-105 transition-all duration-300">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                  <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                  </svg>
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-semibold text-slate-600 truncate mb-1">Total Sent</dt>
                  <dd class="text-2xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent"><%= number_with_delimiter(@campaign_stats[:total_sent]) %></dd>
                </dl>
              </div>
            </div>
          </div>

          <!-- Total Opened -->
          <div class="group bg-white/70 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:scale-105 transition-all duration-300">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="p-3 bg-gradient-to-br from-emerald-500 to-green-600 rounded-xl shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                  <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-semibold text-slate-600 truncate mb-1">Opened</dt>
                  <dd class="text-2xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
                    <%= number_with_delimiter(@campaign_stats[:total_opened]) %>
                    <div class="text-sm font-medium text-emerald-600 mt-1">
                      <%= @campaign_stats[:total_sent] > 0 ? number_to_percentage((@campaign_stats[:total_opened].to_f / @campaign_stats[:total_sent] * 100), precision: 1) : '0%' %>
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>

          <!-- Total Clicked -->
          <div class="group bg-white/70 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:scale-105 transition-all duration-300">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="p-3 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                  <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" />
                  </svg>
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-semibold text-slate-600 truncate mb-1">Clicked</dt>
                  <dd class="text-2xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
                    <%= number_with_delimiter(@campaign_stats[:total_clicked]) %>
                    <div class="text-sm font-medium text-purple-600 mt-1">
                      <%= @campaign_stats[:total_sent] > 0 ? number_to_percentage((@campaign_stats[:total_clicked].to_f / @campaign_stats[:total_sent] * 100), precision: 1) : '0%' %>
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>

          <!-- Unsubscribed -->
          <div class="group bg-white/70 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-6 hover:shadow-2xl hover:scale-105 transition-all duration-300">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="p-3 bg-gradient-to-br from-red-500 to-rose-600 rounded-xl shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                  <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L5.636 5.636" />
                  </svg>
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-semibold text-slate-600 truncate mb-1">Unsubscribed</dt>
                  <dd class="text-2xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
                    <%= number_with_delimiter(@campaign_stats[:total_unsubscribed]) %>
                    <div class="text-sm font-medium text-red-600 mt-1">
                      <%= @campaign_stats[:total_sent] > 0 ? number_to_percentage((@campaign_stats[:total_unsubscribed].to_f / @campaign_stats[:total_sent] * 100), precision: 1) : '0%' %>
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Campaign Details -->
    <div class="lg:col-span-2">
      <div class="bg-white shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900">Campaign Details</h3>
          <p class="mt-1 max-w-2xl text-sm text-gray-500">Basic information about this campaign.</p>
        </div>
        <div class="border-t border-gray-200">
          <dl>
            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt class="text-sm font-medium text-gray-500">Name</dt>
              <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2"><%= @campaign.name %></dd>
            </div>
            <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt class="text-sm font-medium text-gray-500">Subject</dt>
              <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2"><%= @campaign.subject %></dd>
            </div>
            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt class="text-sm font-medium text-gray-500">From</dt>
              <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                <%= @campaign.from_name %> &lt;<%= @campaign.from_email %>&gt;
              </dd>
            </div>
            <% if @campaign.reply_to.present? %>
              <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-500">Reply To</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2"><%= @campaign.reply_to %></dd>
              </div>
            <% end %>
            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt class="text-sm font-medium text-gray-500">Status</dt>
              <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                  <%= case @campaign.status
                      when 'draft' then 'bg-gray-100 text-gray-800'
                      when 'sending' then 'bg-yellow-100 text-yellow-800'
                      when 'sent' then 'bg-green-100 text-green-800'
                      when 'scheduled' then 'bg-blue-100 text-blue-800'
                      else 'bg-gray-100 text-gray-800'
                      end %>">
                  <%= @campaign.status.humanize %>
                </span>
              </dd>
            </div>
            <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt class="text-sm font-medium text-gray-500">Created</dt>
              <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                <%= @campaign.created_at.strftime("%B %d, %Y at %I:%M %p") %>
              </dd>
            </div>
            <% if @campaign.scheduled_at.present? %>
              <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-500">Scheduled</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                  <%= @campaign.scheduled_at.strftime("%B %d, %Y at %I:%M %p") %>
                </dd>
              </div>
            <% end %>
            <% if @campaign.sent_at.present? %>
              <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-500">Sent</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                  <%= @campaign.sent_at.strftime("%B %d, %Y at %I:%M %p") %>
                </dd>
              </div>
            <% end %>
          </dl>
        </div>
      </div>

      <!-- Email Content Preview -->
      <div class="mt-8 bg-white shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900">Email Content</h3>
          <p class="mt-1 max-w-2xl text-sm text-gray-500">Preview of the email content that will be sent.</p>
        </div>
        <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
          <div class="prose max-w-none">
            <% if @campaign.template %>
              <%= simple_format(@campaign.template.body) %>
            <% else %>
              <p class="text-gray-500 italic">No template associated with this campaign.</p>
            <% end %>
          </div>
        </div>
      </div>
    </div>

    <!-- Sidebar -->
    <div class="lg:col-span-1">
      <!-- Recipients -->
      <div class="bg-white shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900">Recipients</h3>
          <p class="mt-1 max-w-2xl text-sm text-gray-500">Who will receive this campaign.</p>
        </div>
        <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
          <% if @campaign.recipient_type == 'all' %>
            <p class="text-sm text-gray-900">All contacts</p>
            <p class="text-xs text-gray-500"><%= @current_account.contacts.count %> contacts</p>
          <% elsif @campaign.recipient_type == 'subscribed' %>
            <p class="text-sm text-gray-900">Subscribed contacts only</p>
            <p class="text-xs text-gray-500"><%= @current_account.contacts.subscribed.count %> contacts</p>
          <% elsif @campaign.recipient_type == 'tags' && @campaign.tags.any? %>
            <p class="text-sm text-gray-900">Contacts with tags:</p>
            <div class="mt-2 flex flex-wrap gap-1">
              <% @campaign.tags.each do |tag| %>
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  <%= tag.name %>
                </span>
              <% end %>
            </div>
            <p class="text-xs text-gray-500 mt-2">
              <%= @campaign.tags.joins(:contacts).distinct.count('contacts.id') %> contacts
            </p>
          <% else %>
            <p class="text-sm text-gray-500">No recipients selected</p>
          <% end %>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="mt-6 bg-white shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900">Quick Actions</h3>
        </div>
        <div class="border-t border-gray-200">
          <div class="px-4 py-4 space-y-3">
            <%= link_to preview_campaign_path(@campaign), 
                target: "_blank",
                class: "w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
              <svg class="-ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
              Preview Email
            <% end %>
            
            <% if @campaign.draft? %>
              <%= link_to send_test_campaign_path(@campaign), 
                  data: { turbo_method: :post },
                  class: "w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
                <svg class="-ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                </svg>
                Send Test Email
              <% end %>
            <% end %>
            
            <%= link_to new_campaign_path(duplicate: @campaign.id), 
                class: "w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
              <svg class="-ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
              Duplicate Campaign
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>