<% content_for :title, "New Campaign - RapidMarkt" %>

<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 mt-2 mb-12">
  <!-- Page header -->
  <div class="mb-8">
    <nav class="flex" aria-label="Breadcrumb">
      <ol role="list" class="flex items-center space-x-4">
        <li>
          <div>
            <%= link_to campaigns_path, class: "text-gray-400 hover:text-gray-500" do %>
              <svg class="flex-shrink-0 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z" clip-rule="evenodd" />
              </svg>
              <span class="sr-only">Back</span>
            <% end %>
          </div>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="flex-shrink-0 h-5 w-5 text-gray-300" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
            <%= link_to "Campaigns", campaigns_path, class: "ml-4 text-sm font-medium text-gray-500 hover:text-gray-700" %>
          </div>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="flex-shrink-0 h-5 w-5 text-gray-300" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
            <span class="ml-4 text-sm font-medium text-gray-500">New Campaign</span>
          </div>
        </li>
      </ol>
    </nav>
    
    <div class="mt-4">
      <h1 class="text-3xl font-bold text-gray-900">Create New Campaign</h1>
      <p class="mt-2 text-sm text-gray-700">
        Design and schedule your email marketing campaign to reach your audience.
      </p>
    </div>
  </div>

  <!-- Debug: Test visibility -->
  <div class="mb-6 p-4 bg-green-100 border border-green-300 rounded-lg">
    <h3 class="text-green-800 font-semibold">✅ About to render campaign form</h3>
    <p class="text-green-700 text-sm">Campaign object: <%= @campaign.inspect %></p>
    <p class="text-green-700 text-sm">Current account: <%= @current_account&.name %></p>
  </div>

  <!-- Campaign form -->
  <div class="bg-white rounded-lg shadow p-6 mb-6">
    <h3 class="text-lg font-semibold mb-4">Simple Test Form</h3>
    <%= form_with model: @campaign, local: true do |form| %>
      <div class="mb-4">
        <%= form.label :name, "Campaign Name", class: "block text-sm font-medium text-gray-700 mb-2" %>
        <%= form.text_field :name, class: "w-full px-3 py-2 border border-gray-300 rounded-md" %>
      </div>
      <div class="mb-4">
        <%= form.label :subject, "Subject", class: "block text-sm font-medium text-gray-700 mb-2" %>
        <%= form.text_field :subject, class: "w-full px-3 py-2 border border-gray-300 rounded-md" %>
      </div>
      <div>
        <%= form.submit "Create Campaign", class: "bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700" %>
      </div>
    <% end %>
  </div>

  <!-- Original wizard form (commented out for testing) -->
  <%# render 'form', campaign: @campaign %>
</div>