<nav class="bg-white shadow-sm border-b border-gray-200" x-data="{ mobileMenuOpen: false }">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between h-16">
      <!-- Logo and Brand -->
      <div class="flex items-center">
        <%= link_to root_path, class: "flex items-center" do %>
          <div class="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </div>
          <span class="ml-3 text-2xl font-bold text-gray-900">RapidMarkt</span>
        <% end %>
      </div>

      <% if user_signed_in? %>
        <!-- Authenticated User Navigation -->
        <div class="hidden md:flex items-center space-x-8">
          <!-- Main Navigation Links -->
          <div class="flex items-center space-x-6">
            <%= link_to dashboard_path, class: "text-gray-700 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 #{'text-indigo-600 bg-indigo-50' if current_page?(dashboard_path)}" do %>
              <svg class="w-4 h-4 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z" />
              </svg>
              Dashboard
            <% end %>

            <%= link_to campaigns_path, class: "text-gray-700 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 #{'text-indigo-600 bg-indigo-50' if current_page?(campaigns_path)}" do %>
              <svg class="w-4 h-4 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              Campaigns
            <% end %>

            <%= link_to contacts_path, class: "text-gray-700 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 #{'text-indigo-600 bg-indigo-50' if current_page?(contacts_path)}" do %>
              <svg class="w-4 h-4 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
              Contacts
            <% end %>

            <%= link_to templates_path, class: "text-gray-700 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 #{'text-indigo-600 bg-indigo-50' if current_page?(templates_path)}" do %>
              <svg class="w-4 h-4 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Templates
            <% end %>

            <%= link_to analytics_path, class: "text-gray-700 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 #{'text-indigo-600 bg-indigo-50' if current_page?(analytics_path)}" do %>
              <svg class="w-4 h-4 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              Analytics
            <% end %>
          </div>

          <!-- User menu dropdown -->
        <div class="relative ml-3">
          <div>
            <button type="button" class="relative flex rounded-full bg-gray-800 text-sm focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-800" id="user-menu-button" aria-expanded="false" aria-haspopup="true">
              <span class="absolute -inset-1.5"></span>
              <span class="sr-only">Open user menu</span>
              <% if current_user.avatar.attached? %>
                <%= image_tag current_user.avatar_url(size: :small), class: "h-8 w-8 rounded-full object-cover" %>
              <% else %>
                <div class="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                  <span class="text-xs font-medium text-gray-700"><%= current_user.initials %></span>
                </div>
              <% end %>
            </button>
          </div>
          
          <!-- Dropdown menu -->
          <div class="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none hidden" role="menu" aria-orientation="vertical" aria-labelledby="user-menu-button" tabindex="-1" id="user-menu">
            <%= link_to settings_account_path, class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100", role: "menuitem" do %>
              Account Settings
            <% end %>
            <%= link_to destroy_user_session_path, method: :delete, class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100", role: "menuitem" do %>
              Sign out
            <% end %>
          </div>
        </div>

            <!-- Dropdown Menu -->
            <div x-show="open"
                 @click.away="open = false"
                 x-transition:enter="transition ease-out duration-200"
                 x-transition:enter-start="transform opacity-0 scale-95 translate-y-1"
                 x-transition:enter-end="transform opacity-100 scale-100 translate-y-0"
                 x-transition:leave="transition ease-in duration-150"
                 x-transition:leave-start="transform opacity-100 scale-100 translate-y-0"
                 x-transition:leave-end="transform opacity-0 scale-95 translate-y-1"
                 class="origin-top-right absolute right-0 mt-1 w-72 bg-white rounded-2xl shadow-xl border-0 overflow-hidden z-50"
                 style="box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05);">

              <!-- User Info Header -->
              <div class="px-6 py-4 bg-gradient-to-r from-gray-50 to-gray-100">
                <div class="flex items-center">
                  <% if current_user.avatar.attached? %>
                    <img src="<%= current_user.avatar_url(size: 48) %>" alt="<%= current_user.first_name %> <%= current_user.last_name %>" class="w-12 h-12 rounded-full object-cover shadow-sm">
                  <% else %>
                    <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-sm">
                      <span class="text-white font-semibold text-lg"><%= current_user.initials %></span>
                    </div>
                  <% end %>
                  <div class="ml-4">
                    <p class="text-sm font-semibold text-gray-900"><%= current_user.first_name %> <%= current_user.last_name %></p>
                    <p class="text-xs text-gray-600 truncate"><%= current_user.email %></p>
                  </div>
                </div>
              </div>

              <!-- Menu Items -->
              <div class="py-2">
                <%= link_to settings_account_path, class: "group flex items-center px-6 py-3 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-700 transition-all duration-200" do %>
                  <div class="w-8 h-8 bg-gray-100 group-hover:bg-indigo-100 rounded-lg flex items-center justify-center mr-3 transition-colors duration-200">
                    <svg class="w-4 h-4 text-gray-500 group-hover:text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                  <div>
                    <p class="font-medium">Account Settings</p>
                    <p class="text-xs text-gray-500 group-hover:text-indigo-500">Manage your profile</p>
                  </div>
                <% end %>

                <%= link_to "#", class: "group flex items-center px-6 py-3 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-700 transition-all duration-200" do %>
                  <div class="w-8 h-8 bg-gray-100 group-hover:bg-indigo-100 rounded-lg flex items-center justify-center mr-3 transition-colors duration-200">
                    <svg class="w-4 h-4 text-gray-500 group-hover:text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  </div>
                  <div>
                    <p class="font-medium">Team</p>
                    <p class="text-xs text-gray-500 group-hover:text-indigo-500">Manage team members</p>
                  </div>
                <% end %>

                <%= link_to "#", class: "group flex items-center px-6 py-3 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-700 transition-all duration-200" do %>
                  <div class="w-8 h-8 bg-gray-100 group-hover:bg-indigo-100 rounded-lg flex items-center justify-center mr-3 transition-colors duration-200">
                    <svg class="w-4 h-4 text-gray-500 group-hover:text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                    </svg>
                  </div>
                  <div>
                    <p class="font-medium">Billing</p>
                    <p class="text-xs text-gray-500 group-hover:text-indigo-500">Manage subscription</p>
                  </div>
                <% end %>

                <!-- Divider -->
                <div class="my-2 mx-6 border-t border-gray-100"></div>

                <!-- Sign Out -->
                <%= link_to destroy_user_session_path, method: :delete, class: "group flex items-center px-6 py-3 text-sm text-red-600 hover:bg-red-50 hover:text-red-700 transition-all duration-200" do %>
                  <div class="w-8 h-8 bg-red-50 group-hover:bg-red-100 rounded-lg flex items-center justify-center mr-3 transition-colors duration-200">
                    <svg class="w-4 h-4 text-red-500 group-hover:text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                    </svg>
                  </div>
                  <div>
                    <p class="font-medium">Sign out</p>
                    <p class="text-xs text-red-400 group-hover:text-red-500">End your session</p>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
        </div>

        <!-- Mobile menu button -->
        <div class="md:hidden flex items-center">
          <button @click="mobileMenuOpen = !mobileMenuOpen"
                  class="group relative inline-flex items-center justify-center p-3 rounded-xl text-gray-500 hover:text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-all duration-200">
            <span class="sr-only">Open main menu</span>
            <!-- Hamburger Icon -->
            <div class="w-6 h-6 relative">
              <span class="absolute block h-0.5 w-6 bg-current transform transition-all duration-300 ease-in-out"
                    :class="mobileMenuOpen ? 'rotate-45 translate-y-2' : 'translate-y-0'"></span>
              <span class="absolute block h-0.5 w-6 bg-current transform transition-all duration-300 ease-in-out translate-y-2"
                    :class="mobileMenuOpen ? 'opacity-0' : 'opacity-100'"></span>
              <span class="absolute block h-0.5 w-6 bg-current transform transition-all duration-300 ease-in-out translate-y-4"
                    :class="mobileMenuOpen ? '-rotate-45 -translate-y-2' : 'translate-y-0'"></span>
            </div>
          </button>
        </div>

      <% else %>
        <!-- Unauthenticated User Navigation -->
        <div class="hidden md:flex items-center space-x-6">
          <a href="#features" class="text-gray-700 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200">
            Features
          </a>
          <a href="#pricing" class="text-gray-700 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200">
            Pricing
          </a>
          <a href="#" class="text-gray-700 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200">
            Resources
          </a>
          <a href="#" class="text-gray-700 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200">
            Support
          </a>
          
          <div class="flex items-center space-x-4">
            <%= link_to new_user_session_path, class: "text-gray-700 hover:text-indigo-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200" do %>
              Sign in
            <% end %>
            <%= link_to new_user_registration_path, class: "bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-6 py-2 rounded-lg text-sm font-medium hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-all duration-200" do %>
              Start Free Trial
            <% end %>
          </div>
        </div>

        <!-- Mobile menu button for unauthenticated users -->
        <div class="md:hidden flex items-center">
          <button @click="mobileMenuOpen = !mobileMenuOpen"
                  class="group relative inline-flex items-center justify-center p-3 rounded-xl text-gray-500 hover:text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-all duration-200">
            <span class="sr-only">Open main menu</span>
            <!-- Hamburger Icon -->
            <div class="w-6 h-6 relative">
              <span class="absolute block h-0.5 w-6 bg-current transform transition-all duration-300 ease-in-out"
                    :class="mobileMenuOpen ? 'rotate-45 translate-y-2' : 'translate-y-0'"></span>
              <span class="absolute block h-0.5 w-6 bg-current transform transition-all duration-300 ease-in-out translate-y-2"
                    :class="mobileMenuOpen ? 'opacity-0' : 'opacity-100'"></span>
              <span class="absolute block h-0.5 w-6 bg-current transform transition-all duration-300 ease-in-out translate-y-4"
                    :class="mobileMenuOpen ? '-rotate-45 -translate-y-2' : 'translate-y-0'"></span>
            </div>
          </button>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Enhanced Mobile Menu -->
  <div class="md:hidden fixed inset-0 z-50" x-show="mobileMenuOpen" @click.away="mobileMenuOpen = false" style="display: none;">
    <!-- Backdrop -->
    <div class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm"
         x-transition:enter="transition-opacity ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition-opacity ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"></div>

    <!-- Menu Panel -->
    <div class="fixed top-0 right-0 h-full w-80 max-w-sm bg-white shadow-2xl"
         x-transition:enter="transition ease-out duration-300 transform"
         x-transition:enter-start="translate-x-full"
         x-transition:enter-end="translate-x-0"
         x-transition:leave="transition ease-in duration-200 transform"
         x-transition:leave-start="translate-x-0"
         x-transition:leave-end="translate-x-full"
         style="box-shadow: -10px 0 25px -5px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(0, 0, 0, 0.05);">

      <% if user_signed_in? %>
        <!-- Authenticated User Mobile Menu -->
        <div class="h-full flex flex-col">
          <!-- Header with User Info -->
          <div class="px-6 py-6 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-100">
            <div class="flex items-center">
              <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-sm">
                <span class="text-white font-semibold text-lg"><%= current_user.first_name&.first || current_user.email.first.upcase %></span>
              </div>
              <div class="ml-4">
                <p class="text-sm font-semibold text-gray-900"><%= current_user.first_name %> <%= current_user.last_name %></p>
                <p class="text-xs text-gray-600 truncate"><%= current_user.email %></p>
              </div>
            </div>
          </div>

          <!-- Navigation Links -->
          <div class="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
            <%= link_to dashboard_path, class: "group flex items-center px-4 py-3 text-base font-medium text-gray-700 hover:bg-indigo-50 hover:text-indigo-700 rounded-xl transition-all duration-200 #{'bg-indigo-50 text-indigo-700' if current_page?(dashboard_path)}" do %>
              <div class="w-10 h-10 bg-gray-100 group-hover:bg-indigo-100 rounded-lg flex items-center justify-center mr-3 transition-colors duration-200">
                <svg class="w-5 h-5 text-gray-500 group-hover:text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z" />
                </svg>
              </div>
              Dashboard
            <% end %>

            <%= link_to campaigns_path, class: "group flex items-center px-4 py-3 text-base font-medium text-gray-700 hover:bg-indigo-50 hover:text-indigo-700 rounded-xl transition-all duration-200 #{'bg-indigo-50 text-indigo-700' if current_page?(campaigns_path)}" do %>
              <div class="w-10 h-10 bg-gray-100 group-hover:bg-indigo-100 rounded-lg flex items-center justify-center mr-3 transition-colors duration-200">
                <svg class="w-5 h-5 text-gray-500 group-hover:text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              Campaigns
            <% end %>

            <%= link_to contacts_path, class: "group flex items-center px-4 py-3 text-base font-medium text-gray-700 hover:bg-indigo-50 hover:text-indigo-700 rounded-xl transition-all duration-200 #{'bg-indigo-50 text-indigo-700' if current_page?(contacts_path)}" do %>
              <div class="w-10 h-10 bg-gray-100 group-hover:bg-indigo-100 rounded-lg flex items-center justify-center mr-3 transition-colors duration-200">
                <svg class="w-5 h-5 text-gray-500 group-hover:text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              Contacts
            <% end %>

            <%= link_to templates_path, class: "group flex items-center px-4 py-3 text-base font-medium text-gray-700 hover:bg-indigo-50 hover:text-indigo-700 rounded-xl transition-all duration-200 #{'bg-indigo-50 text-indigo-700' if current_page?(templates_path)}" do %>
              <div class="w-10 h-10 bg-gray-100 group-hover:bg-indigo-100 rounded-lg flex items-center justify-center mr-3 transition-colors duration-200">
                <svg class="w-5 h-5 text-gray-500 group-hover:text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              Templates
            <% end %>

            <%= link_to analytics_path, class: "group flex items-center px-4 py-3 text-base font-medium text-gray-700 hover:bg-indigo-50 hover:text-indigo-700 rounded-xl transition-all duration-200 #{'bg-indigo-50 text-indigo-700' if current_page?(analytics_path)}" do %>
              <div class="w-10 h-10 bg-gray-100 group-hover:bg-indigo-100 rounded-lg flex items-center justify-center mr-3 transition-colors duration-200">
                <svg class="w-5 h-5 text-gray-500 group-hover:text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              Analytics
            <% end %>
          </div>

          <!-- Footer with Account Actions -->
          <div class="px-4 py-4 border-t border-gray-100 space-y-2">
            <%= link_to edit_user_registration_path, class: "group flex items-center px-4 py-3 text-sm font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900 rounded-xl transition-all duration-200" do %>
              <div class="w-8 h-8 bg-gray-100 group-hover:bg-gray-200 rounded-lg flex items-center justify-center mr-3 transition-colors duration-200">
                <svg class="w-4 h-4 text-gray-500 group-hover:text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
              Account Settings
            <% end %>

            <%= link_to destroy_user_session_path, method: :delete, class: "group flex items-center px-4 py-3 text-sm font-medium text-red-600 hover:bg-red-50 hover:text-red-700 rounded-xl transition-all duration-200" do %>
              <div class="w-8 h-8 bg-red-50 group-hover:bg-red-100 rounded-lg flex items-center justify-center mr-3 transition-colors duration-200">
                <svg class="w-4 h-4 text-red-500 group-hover:text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
              </div>
              Sign out
            <% end %>
          </div>
        </div>

      <% else %>
        <!-- Unauthenticated User Mobile Menu -->
        <div class="h-full flex flex-col">
          <!-- Header -->
          <div class="px-6 py-6 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-100">
            <div class="flex items-center">
              <div class="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <span class="ml-3 text-xl font-bold text-gray-900">RapidMarkt</span>
            </div>
          </div>

          <!-- Navigation Links -->
          <div class="flex-1 px-4 py-6 space-y-2">
            <a href="#features" class="group flex items-center px-4 py-3 text-base font-medium text-gray-700 hover:bg-indigo-50 hover:text-indigo-700 rounded-xl transition-all duration-200">
              <div class="w-10 h-10 bg-gray-100 group-hover:bg-indigo-100 rounded-lg flex items-center justify-center mr-3 transition-colors duration-200">
                <svg class="w-5 h-5 text-gray-500 group-hover:text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              Features
            </a>

            <a href="#pricing" class="group flex items-center px-4 py-3 text-base font-medium text-gray-700 hover:bg-indigo-50 hover:text-indigo-700 rounded-xl transition-all duration-200">
              <div class="w-10 h-10 bg-gray-100 group-hover:bg-indigo-100 rounded-lg flex items-center justify-center mr-3 transition-colors duration-200">
                <svg class="w-5 h-5 text-gray-500 group-hover:text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
              Pricing
            </a>

            <a href="#" class="group flex items-center px-4 py-3 text-base font-medium text-gray-700 hover:bg-indigo-50 hover:text-indigo-700 rounded-xl transition-all duration-200">
              <div class="w-10 h-10 bg-gray-100 group-hover:bg-indigo-100 rounded-lg flex items-center justify-center mr-3 transition-colors duration-200">
                <svg class="w-5 h-5 text-gray-500 group-hover:text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
              Resources
            </a>

            <a href="#" class="group flex items-center px-4 py-3 text-base font-medium text-gray-700 hover:bg-indigo-50 hover:text-indigo-700 rounded-xl transition-all duration-200">
              <div class="w-10 h-10 bg-gray-100 group-hover:bg-indigo-100 rounded-lg flex items-center justify-center mr-3 transition-colors duration-200">
                <svg class="w-5 h-5 text-gray-500 group-hover:text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
              </div>
              Support
            </a>
          </div>

          <!-- Footer with Auth Actions -->
          <div class="px-4 py-4 border-t border-gray-100 space-y-3">
            <%= link_to new_user_session_path, class: "block w-full text-center px-4 py-3 text-base font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900 rounded-xl transition-all duration-200" do %>
              Sign in
            <% end %>
            <%= link_to new_user_registration_path, class: "block w-full text-center px-4 py-3 text-base font-medium bg-gradient-to-r from-indigo-600 to-purple-600 text-white hover:from-indigo-700 hover:to-purple-700 rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl" do %>
              Start Free Trial
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</nav>

<!-- Alpine.js for dropdown functionality -->
<script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>