<% if flash.any? %>
  <% flash.each do |type, message| %>
    <% 
      # Map Rails flash types to toast types
      toast_type = case type.to_s
                   when 'notice', 'success'
                     'success'
                   when 'alert', 'error'
                     'error'
                   when 'warning'
                     'warning'
                   else
                     'info'
                   end
    %>
    
    <div data-controller="toast"
         data-toast-message-value="<%= message %>"
         data-toast-type-value="<%= toast_type %>"
         data-toast-duration-value="5000"
         data-toast-position-value="top-right"></div>
  <% end %>
<% end %>
