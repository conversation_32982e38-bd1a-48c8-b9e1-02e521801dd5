<% content_for :title, "Account Settings" %>

<div class="max-w-4xl mx-auto py-8">
  <div class="bg-white shadow rounded-lg">
    <div class="px-6 py-4 border-b border-gray-200">
      <h1 class="text-2xl font-bold text-gray-900">Account Settings</h1>
      <p class="text-gray-600 mt-1">Manage your profile and notification preferences</p>
    </div>

    <%= form_with model: current_user, url: account_settings_path, method: :patch, local: true, multipart: true, class: "space-y-6" do |form| %>
      <div class="px-6 py-6">
        <!-- Profile Section -->
        <div class="mb-8">
          <h2 class="text-lg font-medium text-gray-900 mb-4">Profile Information</h2>
          
          <!-- Avatar Upload -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">Profile Picture</label>
            <div class="flex items-center space-x-6">
              <div class="shrink-0">
                <% if current_user.avatar.attached? %>
                  <%= image_tag current_user.avatar_url(size: :large), class: "h-20 w-20 object-cover rounded-full border-2 border-gray-300" %>
                <% else %>
                  <div class="h-20 w-20 rounded-full bg-gray-300 flex items-center justify-center border-2 border-gray-300">
                    <span class="text-xl font-medium text-gray-700"><%= current_user.initials %></span>
                  </div>
                <% end %>
              </div>
              <div class="flex-1">
                <%= form.file_field :avatar, 
                    accept: "image/*", 
                    class: "block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100",
                    onchange: "previewAvatar(this)" %>
                <p class="text-xs text-gray-500 mt-1">PNG, JPG, GIF up to 5MB</p>
              </div>
            </div>
          </div>

          <!-- Name Fields -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <%= form.label :first_name, class: "block text-sm font-medium text-gray-700 mb-1" %>
              <%= form.text_field :first_name, 
                  class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" %>
              <% if current_user.errors[:first_name].any? %>
                <p class="text-red-500 text-xs mt-1"><%= current_user.errors[:first_name].first %></p>
              <% end %>
            </div>
            
            <div>
              <%= form.label :last_name, class: "block text-sm font-medium text-gray-700 mb-1" %>
              <%= form.text_field :last_name, 
                  class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" %>
              <% if current_user.errors[:last_name].any? %>
                <p class="text-red-500 text-xs mt-1"><%= current_user.errors[:last_name].first %></p>
              <% end %>
            </div>
          </div>
        </div>

        <!-- Notification Preferences -->
        <div class="mb-8">
          <h2 class="text-lg font-medium text-gray-900 mb-4">Notification Preferences</h2>
          <div class="space-y-4">
            <div class="flex items-center">
              <%= check_box_tag "user[notification_preferences][email_campaign_sent]", "1", 
                  @notification_preferences["email_campaign_sent"], 
                  class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" %>
              <%= label_tag "user[notification_preferences][email_campaign_sent]", 
                  "Email when campaigns are sent", 
                  class: "ml-2 block text-sm text-gray-900" %>
            </div>
            
            <div class="flex items-center">
              <%= check_box_tag "user[notification_preferences][email_campaign_completed]", "1", 
                  @notification_preferences["email_campaign_completed"], 
                  class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" %>
              <%= label_tag "user[notification_preferences][email_campaign_completed]", 
                  "Email when campaigns are completed", 
                  class: "ml-2 block text-sm text-gray-900" %>
            </div>
            
            <div class="flex items-center">
              <%= check_box_tag "user[notification_preferences][new_subscriber]", "1", 
                  @notification_preferences["new_subscriber"], 
                  class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" %>
              <%= label_tag "user[notification_preferences][new_subscriber]", 
                  "Email when new subscribers join", 
                  class: "ml-2 block text-sm text-gray-900" %>
            </div>
            
            <div class="flex items-center">
              <%= check_box_tag "user[notification_preferences][weekly_report]", "1", 
                  @notification_preferences["weekly_report"], 
                  class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" %>
              <%= label_tag "user[notification_preferences][weekly_report]", 
                  "Weekly performance reports", 
                  class: "ml-2 block text-sm text-gray-900" %>
            </div>
            
            <div class="flex items-center">
              <%= check_box_tag "user[notification_preferences][monthly_report]", "1", 
                  @notification_preferences["monthly_report"], 
                  class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" %>
              <%= label_tag "user[notification_preferences][monthly_report]", 
                  "Monthly performance reports", 
                  class: "ml-2 block text-sm text-gray-900" %>
            </div>
          </div>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end space-x-3">
        <%= link_to "Cancel", dashboard_index_path, 
            class: "px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
        <%= form.submit "Save Changes", 
            class: "px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
      </div>
    <% end %>
  </div>
</div>

<script>
function previewAvatar(input) {
  if (input.files && input.files[0]) {
    const reader = new FileReader();
    
    reader.onload = function(e) {
      const preview = input.closest('.flex').querySelector('img, .h-20');
      if (preview.tagName === 'IMG') {
        preview.src = e.target.result;
      } else {
        // Replace the initials div with an image
        const img = document.createElement('img');
        img.src = e.target.result;
        img.className = 'h-20 w-20 object-cover rounded-full border-2 border-gray-300';
        preview.parentNode.replaceChild(img, preview);
      }
    }
    
    reader.readAsDataURL(input.files[0]);
  }
}
</script>