import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="campaign-form"
export default class extends Controller {
  static targets = ["tagSelection", "scheduleDatetime"]

  connect() {
    console.log("Campaign form controller connected")
    this.setupColorPickers()
    this.setupMediaTypePreview()
  }

  // Handle recipient type changes
  recipientTypeChanged(event) {
    const value = event.target.value
    const tagSelection = this.tagSelectionTarget
    
    if (value === 'tags') {
      tagSelection.classList.remove('hidden')
      // Add smooth slide-down animation
      tagSelection.style.maxHeight = '0'
      tagSelection.style.overflow = 'hidden'
      tagSelection.style.transition = 'max-height 0.3s ease-out'
      
      setTimeout(() => {
        tagSelection.style.maxHeight = tagSelection.scrollHeight + 'px'
      }, 10)
    } else {
      // Add smooth slide-up animation
      tagSelection.style.maxHeight = tagSelection.scrollHeight + 'px'
      tagSelection.style.transition = 'max-height 0.3s ease-in'
      
      setTimeout(() => {
        tagSelection.style.maxHeight = '0'
        setTimeout(() => {
          tagSelection.classList.add('hidden')
        }, 300)
      }, 10)
    }
  }

  // Handle send type changes
  sendTypeChanged(event) {
    const value = event.target.value
    const scheduleDatetime = this.scheduleDatetimeTarget
    
    if (value === 'scheduled') {
      scheduleDatetime.classList.remove('hidden')
      // Add smooth slide-down animation
      scheduleDatetime.style.maxHeight = '0'
      scheduleDatetime.style.overflow = 'hidden'
      scheduleDatetime.style.transition = 'max-height 0.3s ease-out'
      
      setTimeout(() => {
        scheduleDatetime.style.maxHeight = scheduleDatetime.scrollHeight + 'px'
      }, 10)
    } else {
      // Add smooth slide-up animation
      scheduleDatetime.style.maxHeight = scheduleDatetime.scrollHeight + 'px'
      scheduleDatetime.style.transition = 'max-height 0.3s ease-in'
      
      setTimeout(() => {
        scheduleDatetime.style.maxHeight = '0'
        setTimeout(() => {
          scheduleDatetime.classList.add('hidden')
        }, 300)
      }, 10)
    }
  }

  // Setup color picker synchronization
  setupColorPickers() {
    const colorInputs = this.element.querySelectorAll('input[type="color"]')
    
    colorInputs.forEach(colorInput => {
      const textInput = colorInput.nextElementSibling
      
      if (textInput && textInput.type === 'text') {
        // Sync color picker to text input
        colorInput.addEventListener('change', () => {
          textInput.value = colorInput.value
          this.updateColorPreview(textInput, colorInput.value)
        })
        
        // Sync text input to color picker
        textInput.addEventListener('input', () => {
          if (/^#[0-9A-F]{6}$/i.test(textInput.value)) {
            colorInput.value = textInput.value
            this.updateColorPreview(textInput, textInput.value)
          }
        })
        
        // Initialize preview
        if (textInput.value) {
          this.updateColorPreview(textInput, textInput.value)
        }
      }
    })
  }

  // Update color preview
  updateColorPreview(input, color) {
    // Add a subtle border color change to show the selected color
    input.style.borderLeftColor = color
    input.style.borderLeftWidth = '4px'
  }

  // Setup media type preview
  setupMediaTypePreview() {
    const mediaTypeRadios = this.element.querySelectorAll('input[name*="[media_type]"]')
    
    mediaTypeRadios.forEach(radio => {
      radio.addEventListener('change', (event) => {
        this.updateMediaTypePreview(event.target.value)
      })
    })
  }

  // Update media type preview
  updateMediaTypePreview(mediaType) {
    // Add visual feedback for media type selection
    const mediaTypeContainer = this.element.querySelector('[data-media-type-preview]')
    
    if (mediaTypeContainer) {
      // Remove existing preview classes
      mediaTypeContainer.className = mediaTypeContainer.className.replace(/media-type-\w+/g, '')
      // Add new preview class
      mediaTypeContainer.classList.add(`media-type-${mediaType}`)
    }
    
    console.log(`Media type changed to: ${mediaType}`)
    
    // You could add more sophisticated preview logic here
    // For example, showing/hiding relevant form fields based on media type
    this.toggleMediaSpecificFields(mediaType)
  }

  // Toggle media-specific form fields
  toggleMediaSpecificFields(mediaType) {
    const videoFields = this.element.querySelectorAll('[data-media-field="video"]')
    const imageFields = this.element.querySelectorAll('[data-media-field="image"]')
    const audioFields = this.element.querySelectorAll('[data-media-field="audio"]')
    
    // Hide all media-specific fields first
    ;[...videoFields, ...imageFields, ...audioFields].forEach(field => {
      field.style.display = 'none'
    })
    
    // Show relevant fields based on media type
    switch (mediaType) {
      case 'video':
        videoFields.forEach(field => field.style.display = 'block')
        break
      case 'image':
        imageFields.forEach(field => field.style.display = 'block')
        break
      case 'audio':
        audioFields.forEach(field => field.style.display = 'block')
        break
      case 'mixed':
        // Show all fields for mixed media
        ;[...videoFields, ...imageFields, ...audioFields].forEach(field => {
          field.style.display = 'block'
        })
        break
    }
  }

  // Form validation helper
  validateForm() {
    const requiredFields = this.element.querySelectorAll('[required]')
    let isValid = true
    
    requiredFields.forEach(field => {
      if (!field.value.trim()) {
        this.showFieldError(field, 'This field is required')
        isValid = false
      } else {
        this.clearFieldError(field)
      }
    })
    
    return isValid
  }

  // Show field error
  showFieldError(field, message) {
    // Remove existing error
    this.clearFieldError(field)
    
    // Add error styling
    field.classList.add('border-red-500', 'focus:border-red-500', 'focus:ring-red-500')
    
    // Add error message
    const errorDiv = document.createElement('div')
    errorDiv.className = 'mt-1 text-sm text-red-600'
    errorDiv.textContent = message
    errorDiv.setAttribute('data-error-message', '')
    
    field.parentNode.appendChild(errorDiv)
  }

  // Clear field error
  clearFieldError(field) {
    // Remove error styling
    field.classList.remove('border-red-500', 'focus:border-red-500', 'focus:ring-red-500')
    
    // Remove error message
    const errorMessage = field.parentNode.querySelector('[data-error-message]')
    if (errorMessage) {
      errorMessage.remove()
    }
  }
}
