import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="contact-management"
export default class extends Controller {
  static targets = ["selectAll", "contactCheckbox", "bulkActions"]

  connect() {
    console.log("Contact management controller connected")
    this.updateBulkActions()
  }

  // Toggle all checkboxes
  toggleAll() {
    const isChecked = this.selectAllTarget.checked
    
    this.contactCheckboxTargets.forEach(checkbox => {
      checkbox.checked = isChecked
      this.animateCheckbox(checkbox, isChecked)
    })
    
    this.updateBulkActions()
  }

  // Update selection when individual checkbox changes
  updateSelection() {
    this.updateSelectAllState()
    this.updateBulkActions()
  }

  // Update the "select all" checkbox state
  updateSelectAllState() {
    const totalCheckboxes = this.contactCheckboxTargets.length
    const checkedCheckboxes = this.contactCheckboxTargets.filter(cb => cb.checked).length
    
    if (checkedCheckboxes === 0) {
      this.selectAllTarget.checked = false
      this.selectAllTarget.indeterminate = false
    } else if (checkedCheckboxes === totalCheckboxes) {
      this.selectAllTarget.checked = true
      this.selectAllTarget.indeterminate = false
    } else {
      this.selectAllTarget.checked = false
      this.selectAllTarget.indeterminate = true
    }
  }

  // Update bulk actions visibility and state
  updateBulkActions() {
    const checkedCheckboxes = this.contactCheckboxTargets.filter(cb => cb.checked)
    const hasSelection = checkedCheckboxes.length > 0
    
    // Update bulk actions visibility
    if (this.hasBulkActionsTarget) {
      if (hasSelection) {
        this.bulkActionsTarget.classList.remove('hidden')
        this.animateIn(this.bulkActionsTarget)
      } else {
        this.animateOut(this.bulkActionsTarget)
      }
    }
    
    // Update bulk action buttons
    const bulkButtons = this.element.querySelectorAll('[data-bulk-action]')
    bulkButtons.forEach(button => {
      button.disabled = !hasSelection
      
      if (hasSelection) {
        button.classList.remove('opacity-50', 'cursor-not-allowed')
        button.classList.add('hover:bg-indigo-700')
      } else {
        button.classList.add('opacity-50', 'cursor-not-allowed')
        button.classList.remove('hover:bg-indigo-700')
      }
    })
    
    // Update selection count
    this.updateSelectionCount(checkedCheckboxes.length)
  }

  // Update selection count display
  updateSelectionCount(count) {
    const countElement = this.element.querySelector('[data-selection-count]')
    if (countElement) {
      countElement.textContent = `${count} selected`
      
      // Add animation for count changes
      countElement.style.transform = 'scale(1.1)'
      setTimeout(() => {
        countElement.style.transform = 'scale(1)'
      }, 150)
    }
  }

  // Animate checkbox selection
  animateCheckbox(checkbox, isChecked) {
    const row = checkbox.closest('tr') || checkbox.closest('[data-contact-row]')
    
    if (row) {
      if (isChecked) {
        row.classList.add('bg-indigo-50', 'border-indigo-200')
        row.style.transform = 'scale(1.02)'
        setTimeout(() => {
          row.style.transform = 'scale(1)'
        }, 150)
      } else {
        row.classList.remove('bg-indigo-50', 'border-indigo-200')
      }
    }
  }

  // Animate element in
  animateIn(element) {
    element.style.opacity = '0'
    element.style.transform = 'translateY(-10px)'
    element.style.transition = 'all 0.3s ease-out'
    
    setTimeout(() => {
      element.style.opacity = '1'
      element.style.transform = 'translateY(0)'
    }, 10)
  }

  // Animate element out
  animateOut(element) {
    element.style.transition = 'all 0.3s ease-in'
    element.style.opacity = '0'
    element.style.transform = 'translateY(-10px)'
    
    setTimeout(() => {
      element.classList.add('hidden')
    }, 300)
  }

  // Bulk tag action
  bulkTag() {
    const selectedContacts = this.getSelectedContactIds()
    
    if (selectedContacts.length === 0) {
      this.showNotification('Please select contacts to tag', 'warning')
      return
    }
    
    // You could open a modal here to select tags
    console.log('Bulk tagging contacts:', selectedContacts)
    this.showNotification(`Tagged ${selectedContacts.length} contacts`, 'success')
  }

  // Bulk unsubscribe action
  bulkUnsubscribe() {
    const selectedContacts = this.getSelectedContactIds()
    
    if (selectedContacts.length === 0) {
      this.showNotification('Please select contacts to unsubscribe', 'warning')
      return
    }
    
    if (confirm(`Are you sure you want to unsubscribe ${selectedContacts.length} contacts?`)) {
      console.log('Bulk unsubscribing contacts:', selectedContacts)
      this.showNotification(`Unsubscribed ${selectedContacts.length} contacts`, 'success')
    }
  }

  // Bulk delete action
  bulkDelete() {
    const selectedContacts = this.getSelectedContactIds()
    
    if (selectedContacts.length === 0) {
      this.showNotification('Please select contacts to delete', 'warning')
      return
    }
    
    if (confirm(`Are you sure you want to delete ${selectedContacts.length} contacts? This action cannot be undone.`)) {
      console.log('Bulk deleting contacts:', selectedContacts)
      this.showNotification(`Deleted ${selectedContacts.length} contacts`, 'success')
    }
  }

  // Get selected contact IDs
  getSelectedContactIds() {
    return this.contactCheckboxTargets
      .filter(cb => cb.checked)
      .map(cb => cb.value)
  }

  // Show notification
  showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div')
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`
    
    // Set notification style based on type
    switch (type) {
      case 'success':
        notification.classList.add('bg-green-500', 'text-white')
        break
      case 'warning':
        notification.classList.add('bg-yellow-500', 'text-white')
        break
      case 'error':
        notification.classList.add('bg-red-500', 'text-white')
        break
      default:
        notification.classList.add('bg-blue-500', 'text-white')
    }
    
    notification.textContent = message
    document.body.appendChild(notification)
    
    // Animate in
    setTimeout(() => {
      notification.style.transform = 'translateX(0)'
    }, 10)
    
    // Auto remove after 3 seconds
    setTimeout(() => {
      notification.style.transform = 'translateX(full)'
      setTimeout(() => {
        notification.remove()
      }, 300)
    }, 3000)
  }
}
