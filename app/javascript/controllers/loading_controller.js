import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="loading"
export default class extends Controller {
  static targets = ["content", "skeleton"]
  static values = { delay: Number }

  connect() {
    this.delayValue = this.delayValue || 300 // Default 300ms delay
    this.showLoading()
  }

  showLoading() {
    // Hide content and show skeleton
    if (this.hasContentTarget) {
      this.contentTarget.style.display = 'none'
    }
    
    if (this.hasSkeletonTarget) {
      this.skeletonTarget.style.display = 'block'
    } else {
      this.createDefaultSkeleton()
    }

    // Simulate loading delay
    setTimeout(() => {
      this.hideLoading()
    }, this.delayValue)
  }

  hideLoading() {
    // Hide skeleton and show content with animation
    if (this.hasSkeletonTarget) {
      this.skeletonTarget.style.opacity = '1'
      this.skeletonTarget.style.transition = 'opacity 0.3s ease-out'
      this.skeletonTarget.style.opacity = '0'
      
      setTimeout(() => {
        this.skeletonTarget.style.display = 'none'
      }, 300)
    }

    if (this.hasContentTarget) {
      this.contentTarget.style.opacity = '0'
      this.contentTarget.style.display = 'block'
      this.contentTarget.style.transition = 'opacity 0.3s ease-in'
      
      setTimeout(() => {
        this.contentTarget.style.opacity = '1'
      }, 50)
    }
  }

  createDefaultSkeleton() {
    const skeleton = document.createElement('div')
    skeleton.className = 'animate-pulse space-y-4'
    skeleton.innerHTML = `
      <div class="h-4 bg-gray-200 rounded w-3/4"></div>
      <div class="space-y-2">
        <div class="h-4 bg-gray-200 rounded"></div>
        <div class="h-4 bg-gray-200 rounded w-5/6"></div>
      </div>
      <div class="h-4 bg-gray-200 rounded w-1/2"></div>
    `
    
    this.element.appendChild(skeleton)
    this.skeletonTarget = skeleton
  }

  // Method to manually trigger loading state
  startLoading() {
    this.showLoading()
  }

  // Method to manually stop loading state
  stopLoading() {
    this.hideLoading()
  }
}

// Skeleton component generator
export class SkeletonGenerator {
  static card() {
    return `
      <div class="animate-pulse bg-white rounded-2xl shadow-lg p-8 border-0">
        <div class="flex items-center justify-between mb-6">
          <div class="flex-1">
            <div class="h-6 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div class="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
          <div class="w-10 h-10 bg-gray-200 rounded-xl"></div>
        </div>
        
        <div class="mb-6">
          <div class="bg-gray-100 rounded-xl p-4">
            <div class="space-y-2">
              <div class="h-4 bg-gray-200 rounded"></div>
              <div class="h-4 bg-gray-200 rounded w-5/6"></div>
              <div class="h-4 bg-gray-200 rounded w-3/4"></div>
            </div>
          </div>
        </div>
        
        <div class="mb-6 flex items-center justify-between">
          <div class="flex space-x-4">
            <div class="h-4 bg-gray-200 rounded w-20"></div>
            <div class="h-4 bg-gray-200 rounded w-16"></div>
          </div>
          <div class="h-6 bg-gray-200 rounded-full w-16"></div>
        </div>
        
        <div class="flex space-x-3">
          <div class="flex-1 h-12 bg-gray-200 rounded-xl"></div>
          <div class="flex-1 h-12 bg-gray-200 rounded-xl"></div>
        </div>
      </div>
    `
  }

  static table() {
    return `
      <div class="animate-pulse bg-white rounded-2xl shadow-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-100">
          <div class="h-6 bg-gray-200 rounded w-1/4"></div>
        </div>
        <div class="divide-y divide-gray-100">
          ${Array(5).fill().map(() => `
            <div class="px-6 py-4 flex items-center space-x-4">
              <div class="w-10 h-10 bg-gray-200 rounded-full"></div>
              <div class="flex-1 space-y-2">
                <div class="h-4 bg-gray-200 rounded w-1/3"></div>
                <div class="h-3 bg-gray-200 rounded w-1/4"></div>
              </div>
              <div class="h-4 bg-gray-200 rounded w-20"></div>
              <div class="h-8 bg-gray-200 rounded w-16"></div>
            </div>
          `).join('')}
        </div>
      </div>
    `
  }

  static chart() {
    return `
      <div class="animate-pulse bg-white rounded-2xl shadow-lg p-6">
        <div class="h-6 bg-gray-200 rounded w-1/3 mb-6"></div>
        <div class="h-64 bg-gray-100 rounded-xl flex items-end justify-between p-4">
          ${Array(7).fill().map((_, i) => `
            <div class="bg-gray-200 rounded-t" style="height: ${Math.random() * 80 + 20}%; width: 12%;"></div>
          `).join('')}
        </div>
        <div class="mt-4 flex justify-center space-x-4">
          <div class="h-3 bg-gray-200 rounded w-16"></div>
          <div class="h-3 bg-gray-200 rounded w-16"></div>
        </div>
      </div>
    `
  }

  static list() {
    return `
      <div class="animate-pulse space-y-4">
        ${Array(6).fill().map(() => `
          <div class="bg-white rounded-xl shadow-sm p-4 border border-gray-100">
            <div class="flex items-center space-x-4">
              <div class="w-12 h-12 bg-gray-200 rounded-xl"></div>
              <div class="flex-1 space-y-2">
                <div class="h-4 bg-gray-200 rounded w-3/4"></div>
                <div class="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
              <div class="h-8 bg-gray-200 rounded w-20"></div>
            </div>
          </div>
        `).join('')}
      </div>
    `
  }

  static dashboard() {
    return `
      <div class="animate-pulse space-y-8">
        <!-- Stats Grid -->
        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          ${Array(4).fill().map(() => `
            <div class="bg-white rounded-2xl shadow-lg p-6">
              <div class="flex items-center justify-between">
                <div class="flex-1">
                  <div class="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                  <div class="h-8 bg-gray-200 rounded w-3/4"></div>
                </div>
                <div class="w-12 h-12 bg-gray-200 rounded-xl"></div>
              </div>
            </div>
          `).join('')}
        </div>
        
        <!-- Chart Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div class="bg-white rounded-2xl shadow-lg p-6">
            <div class="h-6 bg-gray-200 rounded w-1/3 mb-6"></div>
            <div class="h-64 bg-gray-100 rounded-xl"></div>
          </div>
          <div class="bg-white rounded-2xl shadow-lg p-6">
            <div class="h-6 bg-gray-200 rounded w-1/3 mb-6"></div>
            <div class="space-y-4">
              ${Array(5).fill().map(() => `
                <div class="flex items-center justify-between">
                  <div class="h-4 bg-gray-200 rounded w-1/3"></div>
                  <div class="h-4 bg-gray-200 rounded w-16"></div>
                </div>
              `).join('')}
            </div>
          </div>
        </div>
      </div>
    `
  }
}
