import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="search"
export default class extends Controller {
  static targets = ["input", "results", "filters", "clearButton", "noResults", "loading"]
  static values = { 
    url: String, 
    debounce: Number,
    minLength: Number,
    placeholder: String 
  }

  connect() {
    this.debounceValue = this.debounceValue || 300
    this.minLengthValue = this.minLengthValue || 2
    this.timeout = null
    this.abortController = null
    
    this.setupSearch()
  }

  disconnect() {
    if (this.timeout) {
      clearTimeout(this.timeout)
    }
    if (this.abortController) {
      this.abortController.abort()
    }
  }

  setupSearch() {
    // Add search icon and clear button to input
    this.enhanceSearchInput()
    
    // Set up initial state
    this.updateClearButton()
  }

  enhanceSearchInput() {
    const inputContainer = this.inputTarget.parentElement
    
    // Add search icon if not present
    if (!inputContainer.querySelector('.search-icon')) {
      const searchIcon = document.createElement('div')
      searchIcon.className = 'absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none search-icon'
      searchIcon.innerHTML = `
        <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
      `
      inputContainer.insertBefore(searchIcon, this.inputTarget)
      
      // Add padding to input for icon
      this.inputTarget.classList.add('pl-10')
    }

    // Add clear button if not present
    if (!inputContainer.querySelector('.clear-button') && this.hasClearButtonTarget) {
      this.clearButtonTarget.className = 'absolute inset-y-0 right-0 pr-3 flex items-center clear-button'
      this.clearButtonTarget.innerHTML = `
        <button type="button" class="text-gray-400 hover:text-gray-600 focus:outline-none transition-colors duration-200" data-action="click->search#clear">
          <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      `
      
      // Add padding to input for clear button
      this.inputTarget.classList.add('pr-10')
    }
  }

  // Handle input changes with debouncing
  input() {
    if (this.timeout) {
      clearTimeout(this.timeout)
    }

    this.updateClearButton()

    const query = this.inputTarget.value.trim()
    
    if (query.length === 0) {
      this.clearResults()
      return
    }

    if (query.length < this.minLengthValue) {
      this.showMinLengthMessage()
      return
    }

    this.timeout = setTimeout(() => {
      this.performSearch(query)
    }, this.debounceValue)
  }

  // Perform the actual search
  async performSearch(query) {
    if (this.abortController) {
      this.abortController.abort()
    }

    this.abortController = new AbortController()
    
    try {
      this.showLoading()
      
      const url = new URL(this.urlValue, window.location.origin)
      url.searchParams.set('search', query)
      
      // Add any active filters
      this.addFiltersToUrl(url)

      const response = await fetch(url, {
        signal: this.abortController.signal,
        headers: {
          'Accept': 'text/html',
          'X-Requested-With': 'XMLHttpRequest'
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const html = await response.text()
      this.displayResults(html)
      
    } catch (error) {
      if (error.name !== 'AbortError') {
        console.error('Search error:', error)
        this.showError('Search failed. Please try again.')
      }
    } finally {
      this.hideLoading()
    }
  }

  // Add active filters to search URL
  addFiltersToUrl(url) {
    if (this.hasFiltersTarget) {
      const filterInputs = this.filtersTarget.querySelectorAll('input, select')
      
      filterInputs.forEach(input => {
        if (input.value && input.value !== '') {
          url.searchParams.set(input.name, input.value)
        }
      })
    }
  }

  // Display search results
  displayResults(html) {
    if (this.hasResultsTarget) {
      this.resultsTarget.innerHTML = html
      this.resultsTarget.style.display = 'block'
      
      // Add animation
      this.resultsTarget.style.opacity = '0'
      this.resultsTarget.style.transform = 'translateY(-10px)'
      
      setTimeout(() => {
        this.resultsTarget.style.transition = 'all 0.3s ease-out'
        this.resultsTarget.style.opacity = '1'
        this.resultsTarget.style.transform = 'translateY(0)'
      }, 10)
    }
  }

  // Clear search results
  clearResults() {
    if (this.hasResultsTarget) {
      this.resultsTarget.style.display = 'none'
      this.resultsTarget.innerHTML = ''
    }
  }

  // Clear search input and results
  clear() {
    this.inputTarget.value = ''
    this.clearResults()
    this.updateClearButton()
    this.inputTarget.focus()
  }

  // Update clear button visibility
  updateClearButton() {
    if (this.hasClearButtonTarget) {
      const hasValue = this.inputTarget.value.length > 0
      this.clearButtonTarget.style.display = hasValue ? 'flex' : 'none'
    }
  }

  // Show loading state
  showLoading() {
    if (this.hasLoadingTarget) {
      this.loadingTarget.style.display = 'block'
    }
    
    // Add loading class to input
    this.inputTarget.classList.add('animate-pulse')
  }

  // Hide loading state
  hideLoading() {
    if (this.hasLoadingTarget) {
      this.loadingTarget.style.display = 'none'
    }
    
    // Remove loading class from input
    this.inputTarget.classList.remove('animate-pulse')
  }

  // Show minimum length message
  showMinLengthMessage() {
    if (this.hasNoResultsTarget) {
      this.noResultsTarget.innerHTML = `
        <div class="text-center py-4 text-gray-500">
          <svg class="mx-auto h-8 w-8 text-gray-400 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
          <p class="text-sm">Type at least ${this.minLengthValue} characters to search</p>
        </div>
      `
      this.noResultsTarget.style.display = 'block'
    }
  }

  // Show error message
  showError(message) {
    if (this.hasNoResultsTarget) {
      this.noResultsTarget.innerHTML = `
        <div class="text-center py-4 text-red-500">
          <svg class="mx-auto h-8 w-8 text-red-400 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <p class="text-sm">${message}</p>
        </div>
      `
      this.noResultsTarget.style.display = 'block'
    }
  }

  // Handle filter changes
  filterChanged() {
    const query = this.inputTarget.value.trim()
    
    if (query.length >= this.minLengthValue) {
      // Re-run search with new filters
      if (this.timeout) {
        clearTimeout(this.timeout)
      }
      
      this.timeout = setTimeout(() => {
        this.performSearch(query)
      }, this.debounceValue)
    }
  }

  // Handle keyboard navigation
  keydown(event) {
    // Handle escape key to clear search
    if (event.key === 'Escape') {
      this.clear()
      event.preventDefault()
    }
    
    // Handle enter key to prevent form submission if needed
    if (event.key === 'Enter' && this.inputTarget.value.length < this.minLengthValue) {
      event.preventDefault()
    }
  }
}
