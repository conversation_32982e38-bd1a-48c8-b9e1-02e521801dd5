import { Controller } from "@hotwired/stimulus"
import Chart from "chart.js"

// Connects to data-controller="chart"
export default class extends Controller {
  static values = { 
    type: String, 
    data: Object, 
    options: Object 
  }

  connect() {
    this.createChart()
  }

  disconnect() {
    if (this.chart) {
      this.chart.destroy()
    }
  }

  createChart() {
    const ctx = this.element.getContext('2d')
    
    // Default options for all charts
    const defaultOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'bottom',
          labels: {
            usePointStyle: true,
            padding: 20,
            font: {
              size: 12,
              family: "'Inter', sans-serif"
            }
          }
        },
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleColor: '#fff',
          bodyColor: '#fff',
          borderColor: 'rgba(255, 255, 255, 0.1)',
          borderWidth: 1,
          cornerRadius: 8,
          displayColors: false,
          titleFont: {
            size: 14,
            weight: 'bold'
          },
          bodyFont: {
            size: 13
          }
        }
      },
      scales: this.typeValue === 'line' ? {
        x: {
          grid: {
            display: false
          },
          ticks: {
            font: {
              size: 11,
              family: "'Inter', sans-serif"
            },
            color: '#6B7280'
          }
        },
        y: {
          grid: {
            color: 'rgba(107, 114, 128, 0.1)'
          },
          ticks: {
            font: {
              size: 11,
              family: "'Inter', sans-serif"
            },
            color: '#6B7280'
          }
        }
      } : {}
    }

    // Merge provided options with defaults
    const options = { ...defaultOptions, ...this.optionsValue }

    this.chart = new Chart(ctx, {
      type: this.typeValue,
      data: this.dataValue,
      options: options
    })
  }

  // Method to update chart data
  updateData(newData) {
    if (this.chart) {
      this.chart.data = newData
      this.chart.update()
    }
  }
}
