import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="toast"
export default class extends Controller {
  static values = { 
    message: String, 
    type: String, 
    duration: Number,
    position: String 
  }

  connect() {
    this.typeValue = this.typeValue || 'info'
    this.durationValue = this.durationValue || 4000
    this.positionValue = this.positionValue || 'top-right'

    // Only show toast if there's a message
    if (this.messageValue && this.messageValue.trim() !== '') {
      this.show()
    }
  }

  show() {
    // Set up the toast styling based on type
    this.setupStyling()
    
    // Position the toast
    this.positionToast()
    
    // Animate in
    this.animateIn()
    
    // Auto-hide after duration
    if (this.durationValue > 0) {
      setTimeout(() => {
        this.hide()
      }, this.durationValue)
    }
  }

  hide() {
    this.animateOut(() => {
      this.element.remove()
    })
  }

  setupStyling() {
    // Base classes
    this.element.className = `
      fixed z-50 max-w-sm w-full shadow-lg rounded-2xl pointer-events-auto 
      transform transition-all duration-300 ease-out translate-x-full opacity-0
    `.trim()

    // Type-specific styling
    const typeStyles = {
      success: {
        bg: 'bg-gradient-to-r from-green-500 to-emerald-500',
        text: 'text-white',
        icon: this.getSuccessIcon()
      },
      error: {
        bg: 'bg-gradient-to-r from-red-500 to-rose-500',
        text: 'text-white',
        icon: this.getErrorIcon()
      },
      warning: {
        bg: 'bg-gradient-to-r from-yellow-500 to-orange-500',
        text: 'text-white',
        icon: this.getWarningIcon()
      },
      info: {
        bg: 'bg-gradient-to-r from-blue-500 to-indigo-500',
        text: 'text-white',
        icon: this.getInfoIcon()
      }
    }

    const style = typeStyles[this.typeValue] || typeStyles.info
    this.element.classList.add(...style.bg.split(' '), ...style.text.split(' '))

    // Create toast content
    this.element.innerHTML = `
      <div class="p-4">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <div class="w-6 h-6 text-white">
              ${style.icon}
            </div>
          </div>
          <div class="ml-3 w-0 flex-1">
            <p class="text-sm font-medium text-white">
              ${this.messageValue}
            </p>
          </div>
          <div class="ml-4 flex-shrink-0 flex">
            <button class="inline-flex text-white hover:text-gray-200 focus:outline-none focus:text-gray-200 transition-colors duration-200" 
                    data-action="click->toast#hide">
              <span class="sr-only">Close</span>
              <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    `
  }

  positionToast() {
    const positions = {
      'top-right': { top: '1rem', right: '1rem' },
      'top-left': { top: '1rem', left: '1rem' },
      'bottom-right': { bottom: '1rem', right: '1rem' },
      'bottom-left': { bottom: '1rem', left: '1rem' },
      'top-center': { top: '1rem', left: '50%', transform: 'translateX(-50%)' },
      'bottom-center': { bottom: '1rem', left: '50%', transform: 'translateX(-50%)' }
    }

    const position = positions[this.positionValue] || positions['top-right']
    
    Object.keys(position).forEach(key => {
      this.element.style[key] = position[key]
    })

    // Stack toasts if there are multiple
    this.stackToasts()
  }

  stackToasts() {
    const existingToasts = document.querySelectorAll('[data-controller*="toast"]')
    const index = Array.from(existingToasts).indexOf(this.element)
    
    if (index > 0) {
      const offset = index * 80 // 80px spacing between toasts
      
      if (this.positionValue.includes('top')) {
        this.element.style.top = `${1 + (offset / 16)}rem`
      } else if (this.positionValue.includes('bottom')) {
        this.element.style.bottom = `${1 + (offset / 16)}rem`
      }
    }
  }

  animateIn() {
    // Force reflow
    this.element.offsetHeight
    
    setTimeout(() => {
      this.element.classList.remove('translate-x-full', 'opacity-0')
      this.element.classList.add('translate-x-0', 'opacity-100')
    }, 10)
  }

  animateOut(callback) {
    this.element.classList.remove('translate-x-0', 'opacity-100')
    this.element.classList.add('translate-x-full', 'opacity-0')
    
    setTimeout(() => {
      if (callback) callback()
    }, 300)
  }

  // Icon methods
  getSuccessIcon() {
    return `
      <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
      </svg>
    `
  }

  getErrorIcon() {
    return `
      <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
      </svg>
    `
  }

  getWarningIcon() {
    return `
      <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
      </svg>
    `
  }

  getInfoIcon() {
    return `
      <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    `
  }

  // Static method to create toasts programmatically
  static show(message, type = 'info', options = {}) {
    const toast = document.createElement('div')
    toast.setAttribute('data-controller', 'toast')
    toast.setAttribute('data-toast-message-value', message)
    toast.setAttribute('data-toast-type-value', type)
    
    if (options.duration) {
      toast.setAttribute('data-toast-duration-value', options.duration)
    }
    
    if (options.position) {
      toast.setAttribute('data-toast-position-value', options.position)
    }
    
    document.body.appendChild(toast)
    
    return toast
  }
}

// Global toast helper functions
window.showToast = (message, type = 'info', options = {}) => {
  return ToastController.show(message, type, options)
}

window.showSuccess = (message, options = {}) => {
  return ToastController.show(message, 'success', options)
}

window.showError = (message, options = {}) => {
  return ToastController.show(message, 'error', options)
}

window.showWarning = (message, options = {}) => {
  return ToastController.show(message, 'warning', options)
}

window.showInfo = (message, options = {}) => {
  return ToastController.show(message, 'info', options)
}
