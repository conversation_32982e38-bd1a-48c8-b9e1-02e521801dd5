class User < ApplicationRecord
  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable
  devise :database_authenticatable, :registerable,
         :recoverable, :rememberable, :validatable

  # Active Storage
  has_one_attached :avatar

  # Associations
  belongs_to :account

  # Validations
  validates :first_name, :last_name, presence: true
  validates :role, inclusion: { in: %w[owner admin member] }
  # Avatar validation will be handled by a custom method
  validate :avatar_validation, if: -> { avatar.attached? }

  # Scopes
  scope :admins, -> { where(role: [ "owner", "admin" ]) }
  scope :members, -> { where(role: "member") }

  # Methods
  def full_name
    "#{first_name} #{last_name}".strip
  end

  def admin?
    role.in?([ "owner", "admin" ])
  end

  def owner?
    role == "owner"
  end

  def avatar_url(size: :medium)
    return nil unless avatar.attached?
    
    case size
    when :small
      avatar.variant(resize_to_limit: [40, 40])
    when :medium
      avatar.variant(resize_to_limit: [80, 80])
    when :large
      avatar.variant(resize_to_limit: [200, 200])
    else
      avatar
    end
  end

  def initials
    "#{first_name&.first}#{last_name&.first}".upcase
  end

  private

  def avatar_validation
    return unless avatar.attached?

    # Check file type
    unless avatar.content_type.in?(%w[image/jpeg image/jpg image/png image/gif])
      errors.add(:avatar, 'must be a valid image format (JPEG, JPG, PNG, or GIF)')
    end

    # Check file size
    if avatar.byte_size > 5.megabytes
      errors.add(:avatar, 'should be less than 5MB')
    end
  end
end
