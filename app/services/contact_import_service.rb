class ContactImportService
  require 'csv'

  def initialize(account, file)
    @account = account
    @file = file
  end

  def call
    return { success: false, error: "No file provided" } unless @file
    return { success: false, error: "Invalid file type. Please upload a CSV file." } unless valid_file_type?

    imported_count = 0
    errors = []

    begin
      CSV.foreach(@file.path, headers: true, header_converters: :symbol) do |row|
        next if row.to_hash.values.all?(&:blank?) # Skip empty rows

        contact_params = extract_contact_params(row)
        next unless contact_params[:email].present?

        # Skip if contact with this email already exists
        if @account.contacts.exists?(email: contact_params[:email])
          next
        end

        contact = @account.contacts.build(contact_params)
        
        if contact.save
          imported_count += 1
        else
          errors << "Row #{$.}: #{contact.errors.full_messages.join(', ')}"
        end
      end

      if errors.any? && imported_count == 0
        { success: false, error: "Import failed: #{errors.first}" }
      elsif errors.any?
        { success: true, imported_count: imported_count, warnings: errors }
      else
        { success: true, imported_count: imported_count }
      end

    rescue CSV::MalformedCSVError => e
      { success: false, error: "Invalid CSV format: #{e.message}" }
    rescue => e
      { success: false, error: "Import failed: #{e.message}" }
    end
  end

  private

  def valid_file_type?
    return false unless @file.respond_to?(:original_filename)
    
    allowed_extensions = %w[.csv]
    file_extension = File.extname(@file.original_filename).downcase
    allowed_extensions.include?(file_extension)
  end

  def extract_contact_params(row)
    {
      first_name: row[:first_name]&.strip,
      last_name: row[:last_name]&.strip,
      email: row[:email]&.strip&.downcase,
      phone: row[:phone]&.strip,
      company: row[:company]&.strip,
      status: normalize_status(row[:status])
    }.compact_blank
  end

  def normalize_status(status)
    return 'active' if status.blank?
    
    normalized = status.to_s.strip.downcase
    valid_statuses = %w[active inactive bounced]
    
    valid_statuses.include?(normalized) ? normalized : 'active'
  end
end