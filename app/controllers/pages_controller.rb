class PagesController < ApplicationController
  skip_before_action :authenticate_user!, only: [:landing, :privacy, :terms, :cookies, :gdpr, :help, :contact, :features, :pricing]
  
  def landing
    # Landing page for unauthenticated users
  end

  def privacy
    # Privacy Policy page
  end

  def terms
    # Terms of Service page
  end

  def cookies
    # Cookie Policy page
  end

  def gdpr
    # GDPR Information page
  end

  def help
    # Help Center page
  end

  def contact
    # Contact Support page
  end

  def features
    # Features page
  end

  def pricing
    # Pricing page
  end
end