require 'rails_helper'

RSpec.describe "Contacts", type: :request do
  let(:account) { create(:account) }
  let(:user) { create(:user, account: account) }

  before do
    sign_in user
  end

  describe "GET /contacts/new" do
    it "renders the new contact form" do
      get new_contact_path
      expect(response).to have_http_status(:success)
      expect(response.body).to include("Add New Contact")
    end
  end

  describe "POST /contacts" do
    let(:valid_attributes) do
      {
        first_name: '<PERSON>',
        last_name: '<PERSON><PERSON>',
        email: '<EMAIL>',
        status: 'subscribed'
      }
    end

    context "with valid parameters" do
      it "creates a new contact" do
        expect {
          post contacts_path, params: { contact: valid_attributes }
        }.to change(Contact, :count).by(1)
        
        expect(response).to redirect_to(Contact.last)
        follow_redirect!
        expect(response.body).to include("Contact was successfully created")
      end
    end

    context "with new tags" do
      it "creates new tags and assigns them to contact" do
        expect {
          post contacts_path, params: { 
            contact: valid_attributes.merge(new_tags: 'newsletter, vip, customer')
          }
        }.to change(Tag, :count).by(3).and change(Contact, :count).by(1)
        
        contact = Contact.last
        expect(contact.tag_names).to include('newsletter', 'vip', 'customer')
        expect(response).to redirect_to(contact)
      end

      it "handles comma-separated tags with extra spaces" do
        post contacts_path, params: { 
          contact: valid_attributes.merge(new_tags: ' newsletter , vip , customer ')
        }
        
        contact = Contact.last
        expect(contact.tag_names).to include('newsletter', 'vip', 'customer')
      end

      it "ignores empty tag names" do
        post contacts_path, params: { 
          contact: valid_attributes.merge(new_tags: 'newsletter, , vip, ')
        }
        
        contact = Contact.last
        expect(contact.tag_names).to include('newsletter', 'vip')
        expect(contact.tag_names).not_to include('')
      end

      it "does not create duplicate tags" do
        existing_tag = create(:tag, account: account, name: 'newsletter')
        
        expect {
          post contacts_path, params: { 
            contact: valid_attributes.merge(new_tags: 'newsletter, vip')
          }
        }.to change(Tag, :count).by(1) # Only 'vip' should be created
        
        contact = Contact.last
        expect(contact.tags).to include(existing_tag)
        expect(contact.tag_names).to include('newsletter', 'vip')
      end
    end

    context "with both existing and new tags" do
      let!(:existing_tag) { create(:tag, account: account, name: 'marketing') }

      it "assigns both existing and new tags" do
        expect {
          post contacts_path, params: { 
            contact: valid_attributes.merge(
              tag_ids: [existing_tag.id],
              new_tags: 'newsletter, vip'
            )
          }
        }.to change(Tag, :count).by(2).and change(Contact, :count).by(1)
        
        contact = Contact.last
        expect(contact.tags).to include(existing_tag)
        expect(contact.tag_names).to include('marketing', 'newsletter', 'vip')
      end
    end

    context "with invalid parameters" do
      it "does not create a contact with invalid email" do
        expect {
          post contacts_path, params: { contact: { email: 'invalid-email' } }
        }.not_to change(Contact, :count)
        
        expect(response).to have_http_status(:unprocessable_entity)
        expect(response.body).to include("error")
      end
    end
  end

  describe "PATCH /contacts/:id" do
    let(:contact) { create(:contact, account: account) }

    context "with new tags" do
      it "adds new tags to existing contact" do
        expect {
          patch contact_path(contact), params: { 
            contact: { new_tags: 'updated, modified' }
          }
        }.to change(Tag, :count).by(2)
        
        contact.reload
        expect(contact.tag_names).to include('updated', 'modified')
        expect(response).to redirect_to(contact)
      end
    end
  end
end
