FactoryBot.define do
  factory :account do
    sequence(:name) { |n| "Test Account #{n}" }
    sequence(:subdomain) { |n| "test-account-#{n}" }
    plan { "free" }
    status { "active" }

    trait :starter do
      plan { "starter" }
    end

    trait :professional do
      plan { "professional" }
    end

    trait :enterprise do
      plan { "enterprise" }
    end

    trait :suspended do
      status { "suspended" }
    end

    trait :cancelled do
      status { "cancelled" }
    end
  end
end
