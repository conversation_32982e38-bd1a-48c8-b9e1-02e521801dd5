FactoryBot.define do
  factory :tag do
    association :account
    sequence(:name) { |n| "tag-#{n}" }
    color { "#3B82F6" }
    description { "A test tag for organizing contacts" }

    trait :marketing do
      name { "marketing" }
      color { "#10B981" }
      description { "Marketing related contacts" }
    end

    trait :sales do
      name { "sales" }
      color { "#F59E0B" }
      description { "Sales prospects and leads" }
    end

    trait :support do
      name { "support" }
      color { "#EF4444" }
      description { "Customer support contacts" }
    end
  end
end
