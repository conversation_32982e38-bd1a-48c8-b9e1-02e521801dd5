FactoryBot.define do
  factory :contact do
    association :account
    sequence(:email) { |n| "contact#{n}@example.com" }
    first_name { "<PERSON>" }
    last_name { "Doe" }
    status { "subscribed" }
    subscribed_at { Time.current }
    unsubscribed_at { nil }

    trait :unsubscribed do
      status { "unsubscribed" }
      subscribed_at { nil }
      unsubscribed_at { Time.current }
    end

    trait :bounced do
      status { "bounced" }
    end

    trait :complained do
      status { "complained" }
    end
  end
end
