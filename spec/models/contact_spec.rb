require 'rails_helper'

RSpec.describe Contact, type: :model do
  let(:account) { create(:account) }
  let(:contact) { build(:contact, account: account) }

  describe 'associations' do
    it { should belong_to(:account) }
    it { should have_many(:campaign_contacts) }
    it { should have_many(:campaigns) }
    it { should have_many(:contact_tags) }
    it { should have_many(:tags) }
  end

  describe 'validations' do
    it { should validate_presence_of(:email) }
    it { should validate_length_of(:first_name).is_at_most(50) }
    it { should validate_length_of(:last_name).is_at_most(50) }
    it { should validate_inclusion_of(:status).in_array(%w[subscribed unsubscribed bounced complained]) }

    it 'validates email format' do
      contact.email = 'invalid-email'
      expect(contact).not_to be_valid
      expect(contact.errors[:email]).to include('is invalid')
    end

    it 'validates email uniqueness within account scope' do
      existing_contact = create(:contact, account: account, email: '<EMAIL>')
      contact.email = '<EMAIL>'
      expect(contact).not_to be_valid
      expect(contact.errors[:email]).to include('has already been taken')
    end
  end

  describe 'callbacks' do
    it 'normalizes email before validation' do
      contact.email = '  <EMAIL>  '
      contact.valid?
      expect(contact.email).to eq('<EMAIL>')
    end

    it 'sets subscribed_at when status changes to subscribed' do
      contact.status = 'unsubscribed'
      contact.subscribed_at = nil
      contact.save!

      expect {
        contact.update!(status: 'subscribed')
      }.to change { contact.reload.subscribed_at }.from(nil)
    end
  end

  describe 'tag management' do
    let!(:existing_tag) { create(:tag, account: account, name: 'existing') }

    describe '#add_tag' do
      it 'adds an existing tag by name' do
        contact.save!
        contact.add_tag('existing')
        expect(contact.tags).to include(existing_tag)
      end

      it 'creates and adds a new tag by name' do
        contact.save!
        expect {
          contact.add_tag('new_tag')
        }.to change { account.tags.count }.by(1)

        new_tag = account.tags.find_by(name: 'new_tag')
        expect(contact.tags).to include(new_tag)
      end

      it 'does not duplicate tags' do
        contact.save!
        contact.add_tag('existing')
        contact.add_tag('existing')
        expect(contact.tags.where(name: 'existing').count).to eq(1)
      end
    end

    describe '#remove_tag' do
      it 'removes a tag by name' do
        contact.save!
        contact.add_tag('existing')
        contact.remove_tag('existing')
        expect(contact.tags).not_to include(existing_tag)
      end
    end

    describe '#has_tag?' do
      it 'returns true if contact has the tag' do
        contact.save!
        contact.add_tag('existing')
        expect(contact.has_tag?('existing')).to be true
      end

      it 'returns false if contact does not have the tag' do
        contact.save!
        expect(contact.has_tag?('nonexistent')).to be false
      end
    end
  end

  describe 'status methods' do
    it 'correctly identifies subscribed status' do
      contact.status = 'subscribed'
      expect(contact).to be_subscribed
      expect(contact).to be_active
    end

    it 'correctly identifies unsubscribed status' do
      contact.status = 'unsubscribed'
      expect(contact).to be_unsubscribed
      expect(contact).not_to be_active
    end
  end

  describe '#display_name' do
    it 'returns full name when available' do
      contact.first_name = 'John'
      contact.last_name = 'Doe'
      expect(contact.display_name).to eq('John Doe')
    end

    it 'returns email when name is not available' do
      contact.first_name = ''
      contact.last_name = ''
      contact.email = '<EMAIL>'
      expect(contact.display_name).to eq('<EMAIL>')
    end
  end
end
