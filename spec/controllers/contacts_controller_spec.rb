require 'rails_helper'

RSpec.describe ContactsController, type: :controller do
  let(:account) { create(:account) }
  let(:user) { create(:user, account: account) }
  let(:contact) { create(:contact, account: account) }

  before do
    sign_in user
    allow(controller).to receive(:current_user).and_return(user)
    allow(controller).to receive(:set_current_account)
    controller.instance_variable_set(:@current_account, account)
  end

  describe 'GET #new' do
    it 'assigns a new contact' do
      get :new
      expect(assigns(:contact)).to be_a_new(Contact)
      expect(assigns(:contact).account).to eq(account)
    end

    it 'loads available tags' do
      tag1 = create(:tag, account: account, name: 'marketing')
      tag2 = create(:tag, account: account, name: 'sales')
      
      get :new
      expect(assigns(:tags)).to include(tag1, tag2)
    end
  end

  describe 'POST #create' do
    let(:valid_attributes) do
      {
        first_name: '<PERSON>',
        last_name: 'Doe',
        email: '<EMAIL>',
        status: 'subscribed'
      }
    end

    context 'with valid parameters' do
      it 'creates a new contact' do
        expect {
          post :create, params: { contact: valid_attributes }
        }.to change(Contact, :count).by(1)
      end

      it 'redirects to the contact' do
        post :create, params: { contact: valid_attributes }
        expect(response).to redirect_to(Contact.last)
      end

      it 'sets the account correctly' do
        post :create, params: { contact: valid_attributes }
        expect(Contact.last.account).to eq(account)
      end
    end

    context 'with existing tags' do
      let!(:existing_tag) { create(:tag, account: account, name: 'marketing') }

      it 'assigns existing tags to contact' do
        post :create, params: { 
          contact: valid_attributes.merge(tag_ids: [existing_tag.id])
        }
        
        contact = Contact.last
        expect(contact.tags).to include(existing_tag)
      end
    end

    context 'with new tags' do
      it 'creates new tags and assigns them to contact' do
        expect {
          post :create, params: { 
            contact: valid_attributes.merge(new_tags: 'newsletter, vip, customer')
          }
        }.to change(Tag, :count).by(3)
        
        contact = Contact.last
        expect(contact.tag_names).to include('newsletter', 'vip', 'customer')
      end

      it 'handles comma-separated tags with extra spaces' do
        post :create, params: { 
          contact: valid_attributes.merge(new_tags: ' newsletter , vip , customer ')
        }
        
        contact = Contact.last
        expect(contact.tag_names).to include('newsletter', 'vip', 'customer')
      end

      it 'ignores empty tag names' do
        post :create, params: { 
          contact: valid_attributes.merge(new_tags: 'newsletter, , vip, ')
        }
        
        contact = Contact.last
        expect(contact.tag_names).to include('newsletter', 'vip')
        expect(contact.tag_names).not_to include('')
      end

      it 'does not create duplicate tags' do
        existing_tag = create(:tag, account: account, name: 'newsletter')
        
        expect {
          post :create, params: { 
            contact: valid_attributes.merge(new_tags: 'newsletter, vip')
          }
        }.to change(Tag, :count).by(1) # Only 'vip' should be created
        
        contact = Contact.last
        expect(contact.tags).to include(existing_tag)
        expect(contact.tag_names).to include('newsletter', 'vip')
      end
    end

    context 'with both existing and new tags' do
      let!(:existing_tag) { create(:tag, account: account, name: 'marketing') }

      it 'assigns both existing and new tags' do
        expect {
          post :create, params: { 
            contact: valid_attributes.merge(
              tag_ids: [existing_tag.id],
              new_tags: 'newsletter, vip'
            )
          }
        }.to change(Tag, :count).by(2)
        
        contact = Contact.last
        expect(contact.tags).to include(existing_tag)
        expect(contact.tag_names).to include('marketing', 'newsletter', 'vip')
      end
    end

    context 'with invalid parameters' do
      it 'does not create a contact' do
        expect {
          post :create, params: { contact: { email: 'invalid-email' } }
        }.not_to change(Contact, :count)
      end

      it 'renders the new template' do
        post :create, params: { contact: { email: 'invalid-email' } }
        expect(response).to render_template(:new)
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end

  describe 'PATCH #update' do
    context 'with new tags' do
      it 'adds new tags to existing contact' do
        expect {
          patch :update, params: { 
            id: contact.id, 
            contact: { new_tags: 'updated, modified' }
          }
        }.to change(Tag, :count).by(2)
        
        contact.reload
        expect(contact.tag_names).to include('updated', 'modified')
      end
    end
  end
end
